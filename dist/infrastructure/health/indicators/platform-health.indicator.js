"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const platform_integration_strategy_1 = require("../../integrations/strategy/platform-integration.strategy");
let PlatformHealthIndicator = class PlatformHealthIndicator extends terminus_1.HealthIndicator {
    platformIntegrationStrategy;
    constructor(platformIntegrationStrategy) {
        super();
        this.platformIntegrationStrategy = platformIntegrationStrategy;
    }
    async isHealthy(key) {
        try {
            const startTime = Date.now();
            const healthMap = await this.platformIntegrationStrategy.checkAllPlatformsHealth();
            const totalPlatforms = healthMap.size;
            const healthyPlatforms = Array.from(healthMap.values()).filter(Boolean).length;
            const healthyPercentage = totalPlatforms > 0 ? (healthyPlatforms / totalPlatforms) * 100 : 100;
            const responseTime = Date.now() - startTime;
            const isHealthy = healthyPercentage >= 50;
            const platformDetails = {};
            healthMap.forEach((healthy, platformId) => {
                platformDetails[platformId] = healthy;
            });
            const result = this.getStatus(key, isHealthy, {
                totalPlatforms,
                healthyPlatforms,
                healthyPercentage: `${healthyPercentage.toFixed(1)}%`,
                responseTime: `${responseTime}ms`,
                platforms: platformDetails,
            });
            if (!isHealthy) {
                throw new terminus_1.HealthCheckError('Platform health check failed', result);
            }
            return result;
        }
        catch (error) {
            const result = this.getStatus(key, false, {
                error: error.message,
                totalPlatforms: 0,
                healthyPlatforms: 0,
            });
            throw new terminus_1.HealthCheckError('Platform health check failed', result);
        }
    }
};
exports.PlatformHealthIndicator = PlatformHealthIndicator;
exports.PlatformHealthIndicator = PlatformHealthIndicator = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [platform_integration_strategy_1.PlatformIntegrationStrategy])
], PlatformHealthIndicator);
//# sourceMappingURL=platform-health.indicator.js.map