import { PlatformStatus } from '../enums/platform-status.enum';

export interface PlatformConfiguration {
  apiEndpoint?: string;
  apiKey?: string;
  rateLimit?: number;
  timeout?: number;
  retryAttempts?: number;
}

export interface PlatformMetadata {
  website: string;
  description: string;
  supportedCountries: string[];
  supportedCurrencies: string[];
  categories: string[];
}

export class CashbackPlatform {
  constructor(
    private readonly _id: string,
    private readonly _name: string,
    private readonly _status: PlatformStatus,
    private readonly _metadata: PlatformMetadata,
    private readonly _configuration: PlatformConfiguration,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date(),
  ) {
    this.validateId();
    this.validateName();
    this.validateMetadata();
  }

  get id(): string {
    return this._id;
  }

  get name(): string {
    return this._name;
  }

  get status(): PlatformStatus {
    return this._status;
  }

  get metadata(): PlatformMetadata {
    return this._metadata;
  }

  get configuration(): PlatformConfiguration {
    return this._configuration;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  private validateId(): void {
    if (!this._id || this._id.trim().length === 0) {
      throw new Error('Platform ID cannot be empty');
    }
  }

  private validateName(): void {
    if (!this._name || this._name.trim().length === 0) {
      throw new Error('Platform name cannot be empty');
    }
  }

  private validateMetadata(): void {
    if (!this._metadata.website) {
      throw new Error('Platform website is required');
    }
    if (!this._metadata.supportedCountries || this._metadata.supportedCountries.length === 0) {
      throw new Error('Platform must support at least one country');
    }
    if (!this._metadata.supportedCurrencies || this._metadata.supportedCurrencies.length === 0) {
      throw new Error('Platform must support at least one currency');
    }
  }

  isActive(): boolean {
    return this._status === PlatformStatus.ACTIVE;
  }

  supportsCountry(countryCode: string): boolean {
    return this._metadata.supportedCountries.includes(countryCode);
  }

  supportsCurrency(currencyCode: string): boolean {
    return this._metadata.supportedCurrencies.includes(currencyCode);
  }

  hasCategory(category: string): boolean {
    return this._metadata.categories.includes(category);
  }

  updateStatus(newStatus: PlatformStatus): CashbackPlatform {
    if (this._status !== newStatus) {
      this._updatedAt = new Date();
      return new CashbackPlatform(
        this._id,
        this._name,
        newStatus,
        this._metadata,
        this._configuration,
        this._createdAt,
        this._updatedAt,
      );
    }
    return this;
  }

  touch(): void {
    this._updatedAt = new Date();
  }

  equals(other: CashbackPlatform): boolean {
    return this._id === other._id;
  }

  toString(): string {
    return `${this._name} (${this._id})`;
  }
}
