import { CashbackPlatform } from '../../domain/entities/cashback-platform.entity';
import { CashbackPlatformRepository, FindPlatformsOptions } from '../../domain/repositories/cashback-platform.repository';
export declare class MockCashbackPlatformRepository implements CashbackPlatformRepository {
    private readonly logger;
    private platforms;
    findById(id: string): Promise<CashbackPlatform | null>;
    findByName(name: string): Promise<CashbackPlatform | null>;
    findAll(options?: FindPlatformsOptions): Promise<CashbackPlatform[]>;
    findActive(): Promise<CashbackPlatform[]>;
    save(platform: CashbackPlatform): Promise<CashbackPlatform>;
    update(id: string, platform: Partial<CashbackPlatform>): Promise<CashbackPlatform>;
    delete(id: string): Promise<void>;
    count(options?: FindPlatformsOptions): Promise<number>;
    exists(id: string): Promise<boolean>;
}
