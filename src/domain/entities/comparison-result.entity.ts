import { Money } from '../value-objects/money.vo';
import { CashbackOffer } from './cashback-offer.entity';
import { ComparisonStatus } from '../enums/platform-status.enum';

export interface ComparisonCriteria {
  spendAmount: Money;
  category?: string;
  merchant?: string;
  country?: string;
  currency?: string;
}

export interface OfferComparison {
  offer: CashbackOffer;
  platformName: string;
  estimatedCashback: Money;
  rank: number;
  score: number;
}

export interface ComparisonMetrics {
  totalOffersEvaluated: number;
  validOffersFound: number;
  averageCashbackRate: number;
  bestCashbackRate: number;
  processingTimeMs: number;
}

export class ComparisonResult {
  constructor(
    private readonly _id: string,
    private readonly _criteria: ComparisonCriteria,
    private readonly _comparisons: OfferComparison[],
    private readonly _metrics: ComparisonMetrics,
    private readonly _status: ComparisonStatus = ComparisonStatus.COMPLETED,
    private readonly _createdAt: Date = new Date(),
    private _expiresAt: Date = new Date(Date.now() + 15 * 60 * 1000), // 15 minutes default
  ) {
    this.validateId();
    this.validateCriteria();
    this.validateComparisons();
    this.sortComparisons();
  }

  get id(): string {
    return this._id;
  }

  get criteria(): ComparisonCriteria {
    return this._criteria;
  }

  get comparisons(): OfferComparison[] {
    return [...this._comparisons];
  }

  get metrics(): ComparisonMetrics {
    return this._metrics;
  }

  get status(): ComparisonStatus {
    return this._status;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get expiresAt(): Date {
    return this._expiresAt;
  }

  private validateId(): void {
    if (!this._id || this._id.trim().length === 0) {
      throw new Error('Comparison result ID cannot be empty');
    }
  }

  private validateCriteria(): void {
    if (!this._criteria.spendAmount) {
      throw new Error('Spend amount is required for comparison');
    }
    if (this._criteria.spendAmount.amount <= 0) {
      throw new Error('Spend amount must be positive');
    }
  }

  private validateComparisons(): void {
    if (!Array.isArray(this._comparisons)) {
      throw new Error('Comparisons must be an array');
    }
  }

  private sortComparisons(): void {
    this._comparisons.sort((a, b) => {
      // Primary sort: by estimated cashback amount (descending)
      const cashbackDiff = b.estimatedCashback.amount - a.estimatedCashback.amount;
      if (cashbackDiff !== 0) {
        return cashbackDiff;
      }
      
      // Secondary sort: by score (descending)
      return b.score - a.score;
    });

    // Update ranks
    this._comparisons.forEach((comparison, index) => {
      comparison.rank = index + 1;
    });
  }

  getBestOffer(): OfferComparison | null {
    return this._comparisons.length > 0 ? this._comparisons[0] : null;
  }

  getTopOffers(count: number = 5): OfferComparison[] {
    return this._comparisons.slice(0, count);
  }

  getOffersByPlatform(platformName: string): OfferComparison[] {
    return this._comparisons.filter(c => c.platformName === platformName);
  }

  getOffersByCategory(category: string): OfferComparison[] {
    return this._comparisons.filter(c => 
      c.offer.metadata.category.toLowerCase() === category.toLowerCase()
    );
  }

  getTotalPotentialSavings(): Money {
    const bestOffer = this.getBestOffer();
    if (!bestOffer) {
      return new Money(0, this._criteria.spendAmount.currency);
    }
    return bestOffer.estimatedCashback;
  }

  isExpired(): boolean {
    return new Date() > this._expiresAt;
  }

  isValid(): boolean {
    return this._status === ComparisonStatus.COMPLETED && !this.isExpired();
  }

  extendExpiry(additionalMinutes: number): void {
    this._expiresAt = new Date(this._expiresAt.getTime() + additionalMinutes * 60 * 1000);
  }

  equals(other: ComparisonResult): boolean {
    return this._id === other._id;
  }

  toString(): string {
    const bestOffer = this.getBestOffer();
    const bestCashback = bestOffer ? bestOffer.estimatedCashback.toString() : 'No offers';
    return `Comparison for ${this._criteria.spendAmount.toString()}: Best cashback ${bestCashback}`;
  }
}
