import { BasePlatformIntegration } from '../base/base-platform-integration';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform } from '../../../domain/entities/cashback-platform.entity';
export declare class SamplePlatformIntegration extends BasePlatformIntegration {
    constructor();
    getPlatformInfo(): Promise<CashbackPlatform>;
    fetchOffers(): Promise<CashbackOffer[]>;
    fetchOffersByCategory(category: string): Promise<CashbackOffer[]>;
    fetchOffersByMerchant(merchant: string): Promise<CashbackOffer[]>;
    validateConfiguration(): Promise<boolean>;
    getSupportedCategories(): Promise<string[]>;
    getSupportedMerchants(): Promise<string[]>;
    protected performHealthCheck(): Promise<void>;
    private mapApiOfferToEntity;
    private mapApiStatusToOfferStatus;
}
