services:
  # Aplicação NestJS
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: cashback-app
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/cashback-comparison
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongo
      - redis
    networks:
      - cashback-network

  # MongoDB 7.0
  mongo:
    image: mongo:7.0
    container_name: cashback-mongo
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=cashback-comparison
    volumes:
      - mongo_data:/data/db
    networks:
      - cashback-network

  # Redis 7.2
  redis:
    image: redis:7.2-alpine
    container_name: cashback-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cashback-network

  # MongoDB Express (Interface Web)
  mongo-express:
    image: mongo-express:1.0.2
    container_name: cashback-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongo
    networks:
      - cashback-network

  # Redis Commander (Interface Web)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: cashback-redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - cashback-network

volumes:
  mongo_data:
    driver: local
  redis_data:
    driver: local

networks:
  cashback-network:
    driver: bridge
