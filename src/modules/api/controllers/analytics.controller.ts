import {
  Controller,
  Get,
  Query,
  Param,
  HttpStatus,
  HttpException,
  Logger,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { IsOptional, IsDateString, IsEnum, IsNumber, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { ComparisonAnalyticsService } from '../../comparison/services/comparison-analytics.service';
import {
  ComparisonAnalyticsDto,
  TrendDataDto,
  PlatformReportDto,
} from '../dto/analytics-response.dto';

class AnalyticsQueryDto {
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;
}

class TrendQueryDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  @Transform(({ value }) => parseInt(value, 10))
  days?: number;

  @IsOptional()
  @IsEnum(['day', 'week', 'month'])
  granularity?: 'day' | 'week' | 'month';
}

@ApiTags('Analytics')
@Controller('analytics')
@UsePipes(new ValidationPipe({ transform: true }))
export class AnalyticsController {
  private readonly logger = new Logger(AnalyticsController.name);

  constructor(
    private readonly analyticsService: ComparisonAnalyticsService,
  ) {}

  @Get('overview')
  @ApiOperation({
    summary: 'Get comparison analytics overview',
    description: 'Retrieve comprehensive analytics about comparison performance and trends',
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for analytics (ISO 8601)',
    required: false,
    example: '2024-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for analytics (ISO 8601)',
    required: false,
    example: '2024-12-31T23:59:59Z',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Analytics overview retrieved successfully',
    type: ComparisonAnalyticsDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error while generating analytics',
  })
  async getAnalyticsOverview(
    @Query() query: AnalyticsQueryDto,
  ): Promise<ComparisonAnalyticsDto> {
    try {
      this.logger.log('Generating analytics overview');

      const startDate = query.startDate ? new Date(query.startDate) : undefined;
      const endDate = query.endDate ? new Date(query.endDate) : undefined;

      const analytics = await this.analyticsService.getComparisonAnalytics(
        startDate,
        endDate,
      );

      return analytics;
    } catch (error) {
      this.logger.error('Failed to generate analytics overview', error);
      throw new HttpException(
        `Failed to generate analytics: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('trends')
  @ApiOperation({
    summary: 'Get trend data',
    description: 'Retrieve trend data for comparisons over time',
  })
  @ApiQuery({
    name: 'days',
    description: 'Number of days to include in trend data',
    required: false,
    example: 30,
  })
  @ApiQuery({
    name: 'granularity',
    description: 'Granularity of trend data',
    required: false,
    example: 'day',
    enum: ['day', 'week', 'month'],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trend data retrieved successfully',
    type: [TrendDataDto],
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error while generating trend data',
  })
  async getTrendData(
    @Query() query: TrendQueryDto,
  ): Promise<TrendDataDto[]> {
    try {
      this.logger.log(`Generating trend data for ${query.days || 30} days`);

      const trendData = await this.analyticsService.getTrendData(
        query.days || 30,
        query.granularity || 'day',
      );

      return trendData;
    } catch (error) {
      this.logger.error('Failed to generate trend data', error);
      throw new HttpException(
        `Failed to generate trend data: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('platforms/:platformId')
  @ApiOperation({
    summary: 'Get platform-specific analytics',
    description: 'Retrieve detailed analytics for a specific platform',
  })
  @ApiParam({
    name: 'platformId',
    description: 'Platform ID to analyze',
    example: 'sample-platform',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Platform analytics retrieved successfully',
    type: PlatformReportDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Platform not found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal server error while generating platform analytics',
  })
  async getPlatformAnalytics(
    @Param('platformId') platformId: string,
  ): Promise<PlatformReportDto> {
    try {
      this.logger.log(`Generating analytics for platform ${platformId}`);

      const report = await this.analyticsService.getPlatformComparisonReport(platformId);

      if (report.totalComparisons === 0) {
        throw new HttpException(
          `No data found for platform: ${platformId}`,
          HttpStatus.NOT_FOUND,
        );
      }

      return report;
    } catch (error) {
      this.logger.error(`Failed to generate platform analytics for ${platformId}`, error);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `Failed to generate platform analytics: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('categories/top')
  @ApiOperation({
    summary: 'Get top categories',
    description: 'Retrieve the most popular categories by comparison count',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of categories to return',
    required: false,
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Top categories retrieved successfully',
  })
  async getTopCategories(
    @Query('limit') limit?: number,
  ): Promise<any[]> {
    try {
      this.logger.log('Getting top categories');

      const analytics = await this.analyticsService.getComparisonAnalytics();
      
      return analytics.topCategories.slice(0, limit || 10);
    } catch (error) {
      this.logger.error('Failed to get top categories', error);
      throw new HttpException(
        `Failed to get top categories: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('merchants/top')
  @ApiOperation({
    summary: 'Get top merchants',
    description: 'Retrieve the most popular merchants by comparison count',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of merchants to return',
    required: false,
    example: 10,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Top merchants retrieved successfully',
  })
  async getTopMerchants(
    @Query('limit') limit?: number,
  ): Promise<any[]> {
    try {
      this.logger.log('Getting top merchants');

      const analytics = await this.analyticsService.getComparisonAnalytics();
      
      return analytics.topMerchants.slice(0, limit || 10);
    } catch (error) {
      this.logger.error('Failed to get top merchants', error);
      throw new HttpException(
        `Failed to get top merchants: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('platforms/performance')
  @ApiOperation({
    summary: 'Get platform performance comparison',
    description: 'Compare performance metrics across all platforms',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Platform performance data retrieved successfully',
  })
  async getPlatformPerformance(): Promise<any[]> {
    try {
      this.logger.log('Getting platform performance data');

      const analytics = await this.analyticsService.getComparisonAnalytics();
      
      return analytics.platformPerformance;
    } catch (error) {
      this.logger.error('Failed to get platform performance data', error);
      throw new HttpException(
        `Failed to get platform performance data: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
