// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the cashback-comparison database
db = db.getSiblingDB('cashback-comparison');

// Create collections with validation
db.createCollection('platforms', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['_id', 'name', 'status', 'metadata'],
      properties: {
        _id: { bsonType: 'string' },
        name: { bsonType: 'string' },
        status: { enum: ['active', 'inactive', 'maintenance', 'deprecated'] },
        metadata: {
          bsonType: 'object',
          required: ['website', 'supportedCountries', 'supportedCurrencies'],
          properties: {
            website: { bsonType: 'string' },
            supportedCountries: { bsonType: 'array' },
            supportedCurrencies: { bsonType: 'array' }
          }
        }
      }
    }
  }
});

db.createCollection('offers', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['_id', 'platformId', 'title', 'cashbackRate', 'terms', 'metadata'],
      properties: {
        _id: { bsonType: 'string' },
        platformId: { bsonType: 'string' },
        title: { bsonType: 'string' },
        status: { enum: ['active', 'expired', 'coming_soon', 'paused'] }
      }
    }
  }
});

db.createCollection('comparisons', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['_id', 'criteria', 'comparisons', 'metrics', 'status'],
      properties: {
        _id: { bsonType: 'string' },
        status: { enum: ['pending', 'completed', 'failed', 'stale'] }
      }
    }
  }
});

// Create indexes for better performance
db.platforms.createIndex({ 'status': 1 });
db.platforms.createIndex({ 'metadata.supportedCountries': 1 });
db.platforms.createIndex({ 'metadata.supportedCurrencies': 1 });

db.offers.createIndex({ 'platformId': 1 });
db.offers.createIndex({ 'status': 1 });
db.offers.createIndex({ 'metadata.category': 1 });
db.offers.createIndex({ 'metadata.merchant': 1 });
db.offers.createIndex({ 'terms.validFrom': 1, 'terms.validUntil': 1 });
db.offers.createIndex({ 'metadata.featured': 1 });

db.comparisons.createIndex({ 'createdAt': 1 });
db.comparisons.createIndex({ 'expiresAt': 1 });
db.comparisons.createIndex({ 'status': 1 });
db.comparisons.createIndex({ 'criteria.spendAmount.currency': 1 });
db.comparisons.createIndex({ 'criteria.category': 1 });
db.comparisons.createIndex({ 'criteria.merchant': 1 });

print('MongoDB initialization completed successfully');
