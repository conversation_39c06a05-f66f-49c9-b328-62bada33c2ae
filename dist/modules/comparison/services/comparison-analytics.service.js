"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ComparisonAnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
let ComparisonAnalyticsService = ComparisonAnalyticsService_1 = class ComparisonAnalyticsService {
    comparisonResultRepository;
    offerRepository;
    logger = new common_1.Logger(ComparisonAnalyticsService_1.name);
    constructor(comparisonResultRepository, offerRepository) {
        this.comparisonResultRepository = comparisonResultRepository;
        this.offerRepository = offerRepository;
    }
    async getComparisonAnalytics(startDate, endDate) {
        this.logger.log('Generating comparison analytics');
        const comparisons = await this.getComparisonsInRange(startDate, endDate);
        if (comparisons.length === 0) {
            return this.getEmptyAnalytics();
        }
        const [categoryStats, merchantStats, platformStats, processingStats,] = await Promise.all([
            this.calculateCategoryStats(comparisons),
            this.calculateMerchantStats(comparisons),
            this.calculatePlatformStats(comparisons),
            this.calculateProcessingStats(comparisons),
        ]);
        return {
            totalComparisons: comparisons.length,
            averageOffersPerComparison: this.calculateAverageOffersPerComparison(comparisons),
            topCategories: categoryStats,
            topMerchants: merchantStats,
            platformPerformance: platformStats,
            averageProcessingTime: processingStats.averageProcessingTime,
            cacheHitRate: processingStats.cacheHitRate,
        };
    }
    async getTrendData(days = 30, granularity = 'day') {
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
        const comparisons = await this.getComparisonsInRange(startDate, endDate);
        return this.groupComparisonsByPeriod(comparisons, granularity);
    }
    async getPlatformComparisonReport(platformId) {
        const comparisons = await this.comparisonResultRepository.findAll({
            limit: 1000,
            notExpired: false,
        });
        const platformComparisons = comparisons.filter(comparison => comparison.comparisons.some(comp => comp.offer.platformId === platformId));
        if (platformComparisons.length === 0) {
            return {
                totalComparisons: 0,
                winRate: 0,
                averageRank: 0,
                averageCashbackRate: 0,
                topCategories: [],
                recentPerformance: [],
            };
        }
        const wins = platformComparisons.filter(comparison => {
            const bestOffer = comparison.getBestOffer();
            return bestOffer?.offer.platformId === platformId;
        }).length;
        const ranks = platformComparisons.map(comparison => {
            const platformComparison = comparison.comparisons.find(comp => comp.offer.platformId === platformId);
            return platformComparison?.rank || 999;
        });
        const cashbackRates = platformComparisons.flatMap(comparison => comparison.comparisons
            .filter(comp => comp.offer.platformId === platformId)
            .map(comp => comp.offer.cashbackRate.percentage));
        const categories = platformComparisons.flatMap(comparison => comparison.comparisons
            .filter(comp => comp.offer.platformId === platformId)
            .map(comp => comp.offer.metadata.category));
        const topCategories = this.getTopItems(categories, 5);
        return {
            totalComparisons: platformComparisons.length,
            winRate: (wins / platformComparisons.length) * 100,
            averageRank: ranks.reduce((sum, rank) => sum + rank, 0) / ranks.length,
            averageCashbackRate: cashbackRates.reduce((sum, rate) => sum + rate, 0) / cashbackRates.length,
            topCategories,
            recentPerformance: this.groupComparisonsByPeriod(platformComparisons.slice(-30), 'day'),
        };
    }
    async getComparisonsInRange(startDate, endDate) {
        return this.comparisonResultRepository.findAll({
            createdAfter: startDate,
            createdBefore: endDate,
            limit: 10000,
        });
    }
    getEmptyAnalytics() {
        return {
            totalComparisons: 0,
            averageOffersPerComparison: 0,
            topCategories: [],
            topMerchants: [],
            platformPerformance: [],
            averageProcessingTime: 0,
            cacheHitRate: 0,
        };
    }
    calculateAverageOffersPerComparison(comparisons) {
        const totalOffers = comparisons.reduce((sum, comparison) => sum + comparison.comparisons.length, 0);
        return totalOffers / comparisons.length;
    }
    async calculateCategoryStats(comparisons) {
        const categoryMap = new Map();
        comparisons.forEach(comparison => {
            if (comparison.criteria.category) {
                const category = comparison.criteria.category;
                const existing = categoryMap.get(category) || { count: 0, totalCashbackRate: 0, offerCount: 0 };
                const categoryOffers = comparison.comparisons.filter(comp => comp.offer.metadata.category === category);
                const avgCashbackRate = categoryOffers.reduce((sum, comp) => sum + comp.offer.cashbackRate.percentage, 0) / categoryOffers.length;
                categoryMap.set(category, {
                    count: existing.count + 1,
                    totalCashbackRate: existing.totalCashbackRate + (avgCashbackRate || 0),
                    offerCount: existing.offerCount + categoryOffers.length,
                });
            }
        });
        return Array.from(categoryMap.entries())
            .map(([category, stats]) => ({
            category,
            comparisonCount: stats.count,
            averageCashbackRate: stats.totalCashbackRate / stats.count,
            totalOffers: stats.offerCount,
        }))
            .sort((a, b) => b.comparisonCount - a.comparisonCount)
            .slice(0, 10);
    }
    async calculateMerchantStats(comparisons) {
        const merchantMap = new Map();
        comparisons.forEach(comparison => {
            if (comparison.criteria.merchant) {
                const merchant = comparison.criteria.merchant;
                const existing = merchantMap.get(merchant) || { count: 0, totalCashbackRate: 0, offerCount: 0 };
                const merchantOffers = comparison.comparisons.filter(comp => comp.offer.metadata.merchant === merchant);
                const avgCashbackRate = merchantOffers.reduce((sum, comp) => sum + comp.offer.cashbackRate.percentage, 0) / merchantOffers.length;
                merchantMap.set(merchant, {
                    count: existing.count + 1,
                    totalCashbackRate: existing.totalCashbackRate + (avgCashbackRate || 0),
                    offerCount: existing.offerCount + merchantOffers.length,
                });
            }
        });
        return Array.from(merchantMap.entries())
            .map(([merchant, stats]) => ({
            merchant,
            comparisonCount: stats.count,
            averageCashbackRate: stats.totalCashbackRate / stats.count,
            totalOffers: stats.offerCount,
        }))
            .sort((a, b) => b.comparisonCount - a.comparisonCount)
            .slice(0, 10);
    }
    async calculatePlatformStats(comparisons) {
        const platformMap = new Map();
        comparisons.forEach(comparison => {
            const bestOffer = comparison.getBestOffer();
            comparison.comparisons.forEach(comp => {
                const platformId = comp.offer.platformId;
                const existing = platformMap.get(platformId) || {
                    totalOffers: 0,
                    totalCashbackRate: 0,
                    wins: 0,
                    totalRank: 0,
                    appearances: 0,
                    name: comp.platformName,
                };
                platformMap.set(platformId, {
                    totalOffers: existing.totalOffers + 1,
                    totalCashbackRate: existing.totalCashbackRate + comp.offer.cashbackRate.percentage,
                    wins: existing.wins + (bestOffer?.offer.platformId === platformId ? 1 : 0),
                    totalRank: existing.totalRank + comp.rank,
                    appearances: existing.appearances + 1,
                    name: comp.platformName,
                });
            });
        });
        return Array.from(platformMap.entries())
            .map(([platformId, stats]) => ({
            platformId,
            platformName: stats.name,
            totalOffers: stats.totalOffers,
            averageCashbackRate: stats.totalCashbackRate / stats.totalOffers,
            winRate: (stats.wins / stats.appearances) * 100,
            averageRank: stats.totalRank / stats.appearances,
        }))
            .sort((a, b) => b.winRate - a.winRate);
    }
    calculateProcessingStats(comparisons) {
        const processingTimes = comparisons.map(comp => comp.metrics.processingTimeMs);
        const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
        const cacheHitRate = 0;
        return {
            averageProcessingTime,
            cacheHitRate,
        };
    }
    groupComparisonsByPeriod(comparisons, granularity) {
        const groups = new Map();
        comparisons.forEach(comparison => {
            const date = this.formatDateByGranularity(comparison.createdAt, granularity);
            const existing = groups.get(date) || { comparisons: 0, totalCashbackRate: 0, categories: [] };
            const avgCashbackRate = comparison.metrics.averageCashbackRate;
            const category = comparison.criteria.category;
            groups.set(date, {
                comparisons: existing.comparisons + 1,
                totalCashbackRate: existing.totalCashbackRate + avgCashbackRate,
                categories: category ? [...existing.categories, category] : existing.categories,
            });
        });
        return Array.from(groups.entries())
            .map(([date, stats]) => ({
            date,
            comparisons: stats.comparisons,
            averageCashbackRate: stats.totalCashbackRate / stats.comparisons,
            topCategory: this.getTopItems(stats.categories, 1)[0] || 'N/A',
        }))
            .sort((a, b) => a.date.localeCompare(b.date));
    }
    formatDateByGranularity(date, granularity) {
        switch (granularity) {
            case 'day':
                return date.toISOString().split('T')[0];
            case 'week':
                const weekStart = new Date(date);
                weekStart.setDate(date.getDate() - date.getDay());
                return weekStart.toISOString().split('T')[0];
            case 'month':
                return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            default:
                return date.toISOString().split('T')[0];
        }
    }
    getTopItems(items, count) {
        const frequency = new Map();
        items.forEach(item => {
            frequency.set(item, (frequency.get(item) || 0) + 1);
        });
        return Array.from(frequency.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, count)
            .map(([item]) => item);
    }
};
exports.ComparisonAnalyticsService = ComparisonAnalyticsService;
exports.ComparisonAnalyticsService = ComparisonAnalyticsService = ComparisonAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object, Object])
], ComparisonAnalyticsService);
//# sourceMappingURL=comparison-analytics.service.js.map