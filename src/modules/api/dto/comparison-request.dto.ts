import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, IsBoolean, Min, Max, IsArray, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';

export class ComparisonRequestDto {
  @ApiProperty({
    description: 'Amount to spend',
    example: 100.00,
    minimum: 0.01,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  spendAmount: number;

  @ApiPropertyOptional({
    description: 'Currency code (ISO 4217)',
    example: 'USD',
    default: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Category to filter offers',
    example: 'shopping',
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({
    description: 'Merchant to filter offers',
    example: 'Amazon',
  })
  @IsOptional()
  @IsString()
  merchant?: string;

  @ApiPropertyOptional({
    description: 'Country code (ISO 3166-1 alpha-2)',
    example: 'US',
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({
    description: 'Whether to use cached results',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  useCache?: boolean;

  @ApiPropertyOptional({
    description: 'Maximum cache age in minutes',
    example: 15,
    minimum: 1,
    maximum: 1440,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1440)
  maxCacheAge?: number;
}

export class ComparisonOptionsDto {
  @ApiPropertyOptional({
    description: 'Include inactive offers',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeInactive?: boolean;

  @ApiPropertyOptional({
    description: 'Maximum number of results to return',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxResults?: number;

  @ApiPropertyOptional({
    description: 'Minimum cashback rate percentage',
    example: 1.0,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  minCashbackRate?: number;

  @ApiPropertyOptional({
    description: 'Specific platform IDs to include',
    example: ['platform1', 'platform2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  platformIds?: string[];
}
