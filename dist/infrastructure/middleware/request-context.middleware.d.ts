import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
export interface RequestContext {
    requestId: string;
    startTime: number;
    userAgent?: string;
    ip: string;
    correlationId?: string;
}
declare global {
    namespace Express {
        interface Request {
            context: RequestContext;
        }
    }
}
export declare class RequestContextMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: NextFunction): void;
    private generateRequestId;
}
