import { Module, Global } from '@nestjs/common';
import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CacheService } from './services/cache.service';
import { ComparisonCacheService } from './services/comparison-cache.service';
import { OfferCacheService } from './services/offer-cache.service';
import { PlatformCacheService } from './services/platform-cache.service';

@Global()
@Module({
  imports: [
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // Use in-memory cache for development to avoid Redis dependency issues
        const nodeEnv = configService.get('app.nodeEnv', 'development');

        if (nodeEnv === 'development') {
          return {
            ttl: configService.get('cache.ttl.comparison', 900),
            max: configService.get('cache.maxItems.comparison', 1000),
          };
        }

        // For production, you can configure Redis here
        const redisConfig = configService.get('cache.redis');
        return {
          // store: redisStore, // Enable this when Redis is properly configured
          ttl: configService.get('cache.ttl.comparison', 900),
          max: configService.get('cache.maxItems.comparison', 1000),
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    CacheService,
    ComparisonCacheService,
    OfferCacheService,
    PlatformCacheService,
  ],
  exports: [
    NestCacheModule,
    CacheService,
    ComparisonCacheService,
    OfferCacheService,
    PlatformCacheService,
  ],
})
export class CacheModule {}
