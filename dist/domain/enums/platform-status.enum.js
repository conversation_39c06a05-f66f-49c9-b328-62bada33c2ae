"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonStatus = exports.OfferStatus = exports.PlatformStatus = void 0;
var PlatformStatus;
(function (PlatformStatus) {
    PlatformStatus["ACTIVE"] = "active";
    PlatformStatus["INACTIVE"] = "inactive";
    PlatformStatus["MAINTENANCE"] = "maintenance";
    PlatformStatus["DEPRECATED"] = "deprecated";
})(PlatformStatus || (exports.PlatformStatus = PlatformStatus = {}));
var OfferStatus;
(function (OfferStatus) {
    OfferStatus["ACTIVE"] = "active";
    OfferStatus["EXPIRED"] = "expired";
    OfferStatus["COMING_SOON"] = "coming_soon";
    OfferStatus["PAUSED"] = "paused";
})(OfferStatus || (exports.OfferStatus = OfferStatus = {}));
var ComparisonStatus;
(function (ComparisonStatus) {
    ComparisonStatus["PENDING"] = "pending";
    ComparisonStatus["COMPLETED"] = "completed";
    ComparisonStatus["FAILED"] = "failed";
    ComparisonStatus["STALE"] = "stale";
})(ComparisonStatus || (exports.ComparisonStatus = ComparisonStatus = {}));
//# sourceMappingURL=platform-status.enum.js.map