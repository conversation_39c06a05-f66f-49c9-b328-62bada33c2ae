import { Modu<PERSON> } from '@nestjs/common';
import { MockCashbackOfferRepository } from './mock-cashback-offer.repository';
import { MockCashbackPlatformRepository } from './mock-cashback-platform.repository';
import { MockComparisonResultRepository } from './mock-comparison-result.repository';

// Create tokens for the repositories
export const CASHBACK_OFFER_REPOSITORY = 'CASHBACK_OFFER_REPOSITORY';
export const CASHBACK_PLATFORM_REPOSITORY = 'CASHBACK_PLATFORM_REPOSITORY';
export const COMPARISON_RESULT_REPOSITORY = 'COMPARISON_RESULT_REPOSITORY';

@Module({
  providers: [
    {
      provide: CASHBACK_OFFER_REPOSITORY,
      useClass: MockCashbackOfferRepository,
    },
    {
      provide: CASHBACK_PLATFORM_REPOSITORY,
      useClass: Mock<PERSON>ashbackPlatformRepository,
    },
    {
      provide: COMPARISON_RESULT_REPOSITORY,
      useClass: MockComparisonResultRepository,
    },
  ],
  exports: [
    CASHBACK_OFFER_REPOSITORY,
    CASHBACK_PLATFORM_REPOSITORY,
    COMPARISON_RESULT_REPOSITORY,
  ],
})
export class RepositoryModule {}
