"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var MockCashbackPlatformRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockCashbackPlatformRepository = void 0;
const common_1 = require("@nestjs/common");
let MockCashbackPlatformRepository = MockCashbackPlatformRepository_1 = class MockCashbackPlatformRepository {
    logger = new common_1.Logger(MockCashbackPlatformRepository_1.name);
    platforms = [];
    async findById(id) {
        return this.platforms.find(platform => platform.id === id) || null;
    }
    async findByName(name) {
        return this.platforms.find(platform => platform.name === name) || null;
    }
    async findAll(options) {
        let filtered = [...this.platforms];
        if (options?.status) {
            filtered = filtered.filter(platform => platform.status === options.status);
        }
        if (options?.country) {
            filtered = filtered.filter(platform => platform.supportsCountry(options.country));
        }
        if (options?.currency) {
            filtered = filtered.filter(platform => platform.supportsCurrency(options.currency));
        }
        if (options?.category) {
            filtered = filtered.filter(platform => platform.hasCategory(options.category));
        }
        if (options?.limit) {
            filtered = filtered.slice(options.offset || 0, (options.offset || 0) + options.limit);
        }
        return filtered;
    }
    async findActive() {
        return this.platforms.filter(platform => platform.isActive());
    }
    async save(platform) {
        const existingIndex = this.platforms.findIndex(p => p.id === platform.id);
        if (existingIndex >= 0) {
            this.platforms[existingIndex] = platform;
        }
        else {
            this.platforms.push(platform);
        }
        this.logger.debug(`Saved platform: ${platform.id}`);
        return platform;
    }
    async update(id, platform) {
        const existing = await this.findById(id);
        if (!existing) {
            throw new Error(`Platform not found: ${id}`);
        }
        const updated = { ...existing, ...platform };
        return this.save(updated);
    }
    async delete(id) {
        const index = this.platforms.findIndex(platform => platform.id === id);
        if (index >= 0) {
            this.platforms.splice(index, 1);
            this.logger.debug(`Deleted platform: ${id}`);
        }
    }
    async count(options) {
        const filtered = await this.findAll(options);
        return filtered.length;
    }
    async exists(id) {
        return this.platforms.some(platform => platform.id === id);
    }
};
exports.MockCashbackPlatformRepository = MockCashbackPlatformRepository;
exports.MockCashbackPlatformRepository = MockCashbackPlatformRepository = MockCashbackPlatformRepository_1 = __decorate([
    (0, common_1.Injectable)()
], MockCashbackPlatformRepository);
//# sourceMappingURL=mock-cashback-platform.repository.js.map