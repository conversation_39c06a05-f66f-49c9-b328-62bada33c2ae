export declare class CategoryStatsDto {
    category: string;
    comparisonCount: number;
    averageCashbackRate: number;
    totalOffers: number;
}
export declare class MerchantStatsDto {
    merchant: string;
    comparisonCount: number;
    averageCashbackRate: number;
    totalOffers: number;
}
export declare class PlatformStatsDto {
    platformId: string;
    platformName: string;
    totalOffers: number;
    averageCashbackRate: number;
    winRate: number;
    averageRank: number;
}
export declare class ComparisonAnalyticsDto {
    totalComparisons: number;
    averageOffersPerComparison: number;
    topCategories: CategoryStatsDto[];
    topMerchants: MerchantStatsDto[];
    platformPerformance: PlatformStatsDto[];
    averageProcessingTime: number;
    cacheHitRate: number;
}
export declare class TrendDataDto {
    date: string;
    comparisons: number;
    averageCashbackRate: number;
    topCategory: string;
}
export declare class PlatformReportDto {
    totalComparisons: number;
    winRate: number;
    averageRank: number;
    averageCashbackRate: number;
    topCategories: string[];
    recentPerformance: TrendDataDto[];
}
