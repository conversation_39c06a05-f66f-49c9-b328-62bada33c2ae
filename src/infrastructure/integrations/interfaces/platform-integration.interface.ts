import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform } from '../../../domain/entities/cashback-platform.entity';

export interface PlatformHealthStatus {
  isHealthy: boolean;
  responseTime: number;
  lastChecked: Date;
  errorMessage?: string;
}

export interface SyncResult {
  success: boolean;
  offersProcessed: number;
  offersAdded: number;
  offersUpdated: number;
  offersRemoved: number;
  errors: string[];
  syncDuration: number;
}

export interface RateLimitInfo {
  remaining: number;
  resetTime: Date;
  limit: number;
}

export interface PlatformIntegration {
  /**
   * Get the platform identifier
   */
  getPlatformId(): string;

  /**
   * Get platform information
   */
  getPlatformInfo(): Promise<CashbackPlatform>;

  /**
   * Check if the platform integration is healthy
   */
  checkHealth(): Promise<PlatformHealthStatus>;

  /**
   * Fetch all available offers from the platform
   */
  fetchOffers(): Promise<CashbackOffer[]>;

  /**
   * Fetch offers by category
   */
  fetchOffersByCategory(category: string): Promise<CashbackOffer[]>;

  /**
   * Fetch offers by merchant
   */
  fetchOffersByMerchant(merchant: string): Promise<CashbackOffer[]>;

  /**
   * Sync offers with the platform (full sync)
   */
  syncOffers(): Promise<SyncResult>;

  /**
   * Sync offers incrementally (only changes since last sync)
   */
  syncOffersIncremental(lastSyncTime: Date): Promise<SyncResult>;

  /**
   * Get current rate limit information
   */
  getRateLimitInfo(): Promise<RateLimitInfo>;

  /**
   * Validate platform configuration
   */
  validateConfiguration(): Promise<boolean>;

  /**
   * Get supported categories
   */
  getSupportedCategories(): Promise<string[]>;

  /**
   * Get supported merchants
   */
  getSupportedMerchants(): Promise<string[]>;
}
