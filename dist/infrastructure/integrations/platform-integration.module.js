"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformIntegrationModule = void 0;
const common_1 = require("@nestjs/common");
const platform_integration_strategy_1 = require("./strategy/platform-integration.strategy");
const sample_platform_integration_1 = require("./implementations/sample-platform.integration");
let PlatformIntegrationModule = class PlatformIntegrationModule {
    integrationStrategy;
    samplePlatformIntegration;
    constructor(integrationStrategy, samplePlatformIntegration) {
        this.integrationStrategy = integrationStrategy;
        this.samplePlatformIntegration = samplePlatformIntegration;
    }
    async onModuleInit() {
        this.integrationStrategy.registerIntegration(this.samplePlatformIntegration);
    }
};
exports.PlatformIntegrationModule = PlatformIntegrationModule;
exports.PlatformIntegrationModule = PlatformIntegrationModule = __decorate([
    (0, common_1.Module)({
        providers: [
            platform_integration_strategy_1.PlatformIntegrationStrategy,
            sample_platform_integration_1.SamplePlatformIntegration,
        ],
        exports: [platform_integration_strategy_1.PlatformIntegrationStrategy],
    }),
    __metadata("design:paramtypes", [platform_integration_strategy_1.PlatformIntegrationStrategy,
        sample_platform_integration_1.SamplePlatformIntegration])
], PlatformIntegrationModule);
//# sourceMappingURL=platform-integration.module.js.map