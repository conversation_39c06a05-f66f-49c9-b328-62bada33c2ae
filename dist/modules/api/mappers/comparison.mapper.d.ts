import { ComparisonResult } from '../../../domain/entities/comparison-result.entity';
import { ComparisonResultDto } from '../dto/comparison-response.dto';
export declare class ComparisonMapper {
    mapComparisonResultToDto(result: ComparisonResult): ComparisonResultDto;
    private mapOfferComparisonToDto;
    private mapCashbackOfferToDto;
    private mapMoneyToDto;
    private mapCashbackRateToDto;
    private mapOfferTermsToDto;
    private mapOfferMetadataToDto;
    private mapComparisonMetricsToDto;
    private mapComparisonCriteriaToDto;
}
