import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { ApiModule } from './modules/api/api.module';
import { CacheModule } from './infrastructure/cache/cache.module';
import { LoggingModule } from './infrastructure/logging/logging.module';
import { HealthModule } from './infrastructure/health/health.module';
import databaseConfig from './config/database.config';
import appConfig from './config/app.config';
import cacheConfig from './config/cache.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, appConfig, cacheConfig],
      envFilePath: ['.env.local', '.env'],
    }),
    LoggingModule,
    DatabaseModule,
    CacheModule,
    HealthModule,
    ApiModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
