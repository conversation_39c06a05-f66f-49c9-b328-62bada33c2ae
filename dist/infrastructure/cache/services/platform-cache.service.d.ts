import { CacheService } from './cache.service';
import { CashbackPlatform } from '../../../domain/entities/cashback-platform.entity';
import { PlatformHealthStatus } from '../../integrations/interfaces/platform-integration.interface';
export declare class PlatformCacheService {
    private readonly cacheService;
    private readonly logger;
    private readonly namespace;
    constructor(cacheService: CacheService);
    getPlatform(id: string): Promise<CashbackPlatform | null>;
    setPlatform(platform: CashbackPlatform): Promise<void>;
    getAllPlatforms(): Promise<CashbackPlatform[]>;
    setAllPlatforms(platforms: CashbackPlatform[]): Promise<void>;
    getActivePlatforms(): Promise<CashbackPlatform[]>;
    setActivePlatforms(platforms: CashbackPlatform[]): Promise<void>;
    getPlatformsByCountry(countryCode: string): Promise<CashbackPlatform[]>;
    setPlatformsByCountry(countryCode: string, platforms: CashbackPlatform[]): Promise<void>;
    getPlatformsByCurrency(currencyCode: string): Promise<CashbackPlatform[]>;
    setPlatformsByCurrency(currencyCode: string, platforms: CashbackPlatform[]): Promise<void>;
    getPlatformHealth(platformId: string): Promise<PlatformHealthStatus | null>;
    setPlatformHealth(platformId: string, health: PlatformHealthStatus): Promise<void>;
    getAllPlatformHealth(): Promise<Map<string, PlatformHealthStatus>>;
    setAllPlatformHealth(healthMap: Map<string, PlatformHealthStatus>): Promise<void>;
    getPlatformCategories(platformId: string): Promise<string[]>;
    setPlatformCategories(platformId: string, categories: string[]): Promise<void>;
    getPlatformMerchants(platformId: string): Promise<string[]>;
    setPlatformMerchants(platformId: string, merchants: string[]): Promise<void>;
    invalidatePlatform(id: string): Promise<void>;
    invalidateAllPlatforms(): Promise<void>;
    invalidateActivePlatforms(): Promise<void>;
    invalidatePlatformHealth(platformId: string): Promise<void>;
    invalidateCountryPlatforms(countryCode: string): Promise<void>;
    invalidateCurrencyPlatforms(currencyCode: string): Promise<void>;
    refreshPlatformCache(platformFactory: () => Promise<CashbackPlatform[]>): Promise<void>;
    private groupPlatformsByCountry;
    private groupPlatformsByCurrency;
    getPlatformStats(): Promise<{
        totalCached: number;
        activePlatforms: number;
        healthyPlatforms: number;
        byCountry: Record<string, number>;
        byCurrency: Record<string, number>;
    }>;
}
