"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonResult = void 0;
const money_vo_1 = require("../value-objects/money.vo");
const platform_status_enum_1 = require("../enums/platform-status.enum");
class ComparisonResult {
    _id;
    _criteria;
    _comparisons;
    _metrics;
    _status;
    _createdAt;
    _expiresAt;
    constructor(_id, _criteria, _comparisons, _metrics, _status = platform_status_enum_1.ComparisonStatus.COMPLETED, _createdAt = new Date(), _expiresAt = new Date(Date.now() + 15 * 60 * 1000)) {
        this._id = _id;
        this._criteria = _criteria;
        this._comparisons = _comparisons;
        this._metrics = _metrics;
        this._status = _status;
        this._createdAt = _createdAt;
        this._expiresAt = _expiresAt;
        this.validateId();
        this.validateCriteria();
        this.validateComparisons();
        this.sortComparisons();
    }
    get id() {
        return this._id;
    }
    get criteria() {
        return this._criteria;
    }
    get comparisons() {
        return [...this._comparisons];
    }
    get metrics() {
        return this._metrics;
    }
    get status() {
        return this._status;
    }
    get createdAt() {
        return this._createdAt;
    }
    get expiresAt() {
        return this._expiresAt;
    }
    validateId() {
        if (!this._id || this._id.trim().length === 0) {
            throw new Error('Comparison result ID cannot be empty');
        }
    }
    validateCriteria() {
        if (!this._criteria.spendAmount) {
            throw new Error('Spend amount is required for comparison');
        }
        if (this._criteria.spendAmount.amount <= 0) {
            throw new Error('Spend amount must be positive');
        }
    }
    validateComparisons() {
        if (!Array.isArray(this._comparisons)) {
            throw new Error('Comparisons must be an array');
        }
    }
    sortComparisons() {
        this._comparisons.sort((a, b) => {
            const cashbackDiff = b.estimatedCashback.amount - a.estimatedCashback.amount;
            if (cashbackDiff !== 0) {
                return cashbackDiff;
            }
            return b.score - a.score;
        });
        this._comparisons.forEach((comparison, index) => {
            comparison.rank = index + 1;
        });
    }
    getBestOffer() {
        return this._comparisons.length > 0 ? this._comparisons[0] : null;
    }
    getTopOffers(count = 5) {
        return this._comparisons.slice(0, count);
    }
    getOffersByPlatform(platformName) {
        return this._comparisons.filter(c => c.platformName === platformName);
    }
    getOffersByCategory(category) {
        return this._comparisons.filter(c => c.offer.metadata.category.toLowerCase() === category.toLowerCase());
    }
    getTotalPotentialSavings() {
        const bestOffer = this.getBestOffer();
        if (!bestOffer) {
            return new money_vo_1.Money(0, this._criteria.spendAmount.currency);
        }
        return bestOffer.estimatedCashback;
    }
    isExpired() {
        return new Date() > this._expiresAt;
    }
    isValid() {
        return this._status === platform_status_enum_1.ComparisonStatus.COMPLETED && !this.isExpired();
    }
    extendExpiry(additionalMinutes) {
        this._expiresAt = new Date(this._expiresAt.getTime() + additionalMinutes * 60 * 1000);
    }
    equals(other) {
        return this._id === other._id;
    }
    toString() {
        const bestOffer = this.getBestOffer();
        const bestCashback = bestOffer ? bestOffer.estimatedCashback.toString() : 'No offers';
        return `Comparison for ${this._criteria.spendAmount.toString()}: Best cashback ${bestCashback}`;
    }
}
exports.ComparisonResult = ComparisonResult;
//# sourceMappingURL=comparison-result.entity.js.map