"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonService = void 0;
const comparison_result_entity_1 = require("../entities/comparison-result.entity");
class ComparisonService {
    scoring;
    defaultScoring = {
        cashbackWeight: 0.6,
        reliabilityWeight: 0.2,
        popularityWeight: 0.1,
        validityWeight: 0.1,
    };
    constructor(scoring = this.defaultScoring) {
        this.scoring = scoring;
        this.validateScoring();
    }
    validateScoring() {
        const totalWeight = Object.values(this.scoring).reduce((sum, weight) => sum + weight, 0);
        if (Math.abs(totalWeight - 1.0) > 0.001) {
            throw new Error('Scoring weights must sum to 1.0');
        }
    }
    compareOffers(criteria, offers, platformNames) {
        const startTime = Date.now();
        const validOffers = offers.filter(offer => this.isOfferEligible(offer, criteria));
        const comparisons = validOffers.map(offer => {
            const platformName = platformNames.get(offer.platformId) || 'Unknown Platform';
            const estimatedCashback = offer.calculateCashback(criteria.spendAmount);
            const score = this.calculateOfferScore(offer, criteria, estimatedCashback);
            return {
                offer,
                platformName,
                estimatedCashback,
                rank: 0,
                score,
            };
        });
        const processingTime = Date.now() - startTime;
        const metrics = this.calculateMetrics(offers, validOffers, comparisons, processingTime);
        const resultId = this.generateComparisonId(criteria);
        return new comparison_result_entity_1.ComparisonResult(resultId, criteria, comparisons, metrics);
    }
    isOfferEligible(offer, criteria) {
        if (!offer.isValid()) {
            return false;
        }
        if (criteria.category && !offer.belongsToCategory(criteria.category)) {
            return false;
        }
        if (criteria.merchant && !offer.belongsToMerchant(criteria.merchant)) {
            return false;
        }
        return true;
    }
    calculateOfferScore(offer, criteria, estimatedCashback) {
        const maxPossibleCashback = criteria.spendAmount.amount;
        const cashbackScore = Math.min(estimatedCashback.amount / maxPossibleCashback, 1);
        const reliabilityScore = this.calculateReliabilityScore(offer);
        const popularityScore = this.calculatePopularityScore(offer);
        const validityScore = this.calculateValidityScore(offer);
        return (cashbackScore * this.scoring.cashbackWeight +
            reliabilityScore * this.scoring.reliabilityWeight +
            popularityScore * this.scoring.popularityWeight +
            validityScore * this.scoring.validityWeight);
    }
    calculateReliabilityScore(offer) {
        let score = 0.7;
        if (offer.metadata.featured) {
            score += 0.2;
        }
        if (offer.terms.description && offer.terms.description.length > 50) {
            score += 0.1;
        }
        return Math.min(score, 1.0);
    }
    calculatePopularityScore(offer) {
        let score = offer.metadata.priority / 10;
        if (offer.metadata.featured) {
            score += 0.3;
        }
        return Math.min(score, 1.0);
    }
    calculateValidityScore(offer) {
        const now = new Date();
        let score = 1.0;
        if (offer.terms.validUntil) {
            const timeUntilExpiry = offer.terms.validUntil.getTime() - now.getTime();
            const daysUntilExpiry = timeUntilExpiry / (1000 * 60 * 60 * 24);
            if (daysUntilExpiry < 1) {
                score *= 0.5;
            }
            else if (daysUntilExpiry < 7) {
                score *= 0.8;
            }
        }
        if (offer.terms.maxRedemptions && offer.terms.currentRedemptions) {
            const redemptionRatio = offer.terms.currentRedemptions / offer.terms.maxRedemptions;
            if (redemptionRatio > 0.9) {
                score *= 0.6;
            }
            else if (redemptionRatio > 0.7) {
                score *= 0.8;
            }
        }
        return score;
    }
    calculateMetrics(allOffers, validOffers, comparisons, processingTimeMs) {
        const cashbackRates = comparisons.map(c => c.offer.cashbackRate.percentage);
        const averageCashbackRate = cashbackRates.length > 0
            ? cashbackRates.reduce((sum, rate) => sum + rate, 0) / cashbackRates.length
            : 0;
        const bestCashbackRate = cashbackRates.length > 0 ? Math.max(...cashbackRates) : 0;
        return {
            totalOffersEvaluated: allOffers.length,
            validOffersFound: validOffers.length,
            averageCashbackRate,
            bestCashbackRate,
            processingTimeMs,
        };
    }
    generateComparisonId(criteria) {
        const timestamp = Date.now();
        const hash = this.simpleHash(JSON.stringify(criteria));
        return `comp_${timestamp}_${hash}`;
    }
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }
}
exports.ComparisonService = ComparisonService;
//# sourceMappingURL=comparison.service.js.map