import { Module, Global } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as path from 'path';

@Global()
@Module({
  imports: [
    WinstonModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const logLevel = configService.get<string>('app.logging.level', 'info');
        const logFilePath = configService.get<string>('app.logging.filePath', 'logs/app.log');
        const nodeEnv = configService.get<string>('app.nodeEnv', 'development');

        const logDir = path.dirname(logFilePath);
        
        // Create custom format
        const customFormat = winston.format.combine(
          winston.format.timestamp({
            format: 'YYYY-MM-DD HH:mm:ss.SSS',
          }),
          winston.format.errors({ stack: true }),
          winston.format.json(),
          winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
            const logEntry: any = {
              timestamp,
              level,
              message,
              ...meta,
            };
            if (context) logEntry.context = context;
            if (trace) logEntry.trace = trace;
            return JSON.stringify(logEntry);
          }),
        );

        const transports: winston.transport[] = [];

        // Console transport for development
        if (nodeEnv === 'development') {
          transports.push(
            new winston.transports.Console({
              level: logLevel,
              format: winston.format.combine(
                winston.format.colorize(),
                winston.format.timestamp({
                  format: 'HH:mm:ss.SSS',
                }),
                winston.format.printf(({ timestamp, level, message, context }) => {
                  const contextStr = context ? ` [${context}]` : '';
                  return `${timestamp} ${level}${contextStr}: ${message}`;
                }),
              ),
            }),
          );
        }

        // File transports for all environments
        transports.push(
          // Combined log file
          new winston.transports.File({
            filename: logFilePath,
            level: logLevel,
            format: customFormat,
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true,
          }),
          // Error log file
          new winston.transports.File({
            filename: path.join(logDir, 'error.log'),
            level: 'error',
            format: customFormat,
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true,
          }),
        );

        // Add HTTP transport for production (optional)
        if (nodeEnv === 'production') {
          const httpEndpoint = configService.get<string>('logging.httpEndpoint');
          if (httpEndpoint) {
            transports.push(
              new winston.transports.Http({
                host: httpEndpoint,
                port: 443,
                path: '/logs',
                ssl: true,
                level: 'error',
                format: customFormat,
              }),
            );
          }
        }

        return {
          level: logLevel,
          format: customFormat,
          transports,
          // Handle uncaught exceptions and rejections
          exceptionHandlers: [
            new winston.transports.File({
              filename: path.join(logDir, 'exceptions.log'),
              format: customFormat,
            }),
          ],
          rejectionHandlers: [
            new winston.transports.File({
              filename: path.join(logDir, 'rejections.log'),
              format: customFormat,
            }),
          ],
          exitOnError: false,
        };
      },
      inject: [ConfigService],
    }),
  ],
  exports: [WinstonModule],
})
export class LoggingModule {}
