import { CashbackOffer } from '../entities/cashback-offer.entity';
import { ComparisonResult, ComparisonCriteria, OfferComparison } from '../entities/comparison-result.entity';
import { Money } from '../value-objects/money.vo';

export interface ComparisonScoring {
  cashbackWeight: number;
  reliabilityWeight: number;
  popularityWeight: number;
  validityWeight: number;
}

export class ComparisonService {
  private readonly defaultScoring: ComparisonScoring = {
    cashbackWeight: 0.6,
    reliabilityWeight: 0.2,
    popularityWeight: 0.1,
    validityWeight: 0.1,
  };

  constructor(private readonly scoring: ComparisonScoring = this.defaultScoring) {
    this.validateScoring();
  }

  private validateScoring(): void {
    const totalWeight = Object.values(this.scoring).reduce((sum, weight) => sum + weight, 0);
    if (Math.abs(totalWeight - 1.0) > 0.001) {
      throw new Error('Scoring weights must sum to 1.0');
    }
  }

  compareOffers(
    criteria: ComparisonCriteria,
    offers: CashbackOffer[],
    platformNames: Map<string, string>,
  ): ComparisonResult {
    const startTime = Date.now();
    
    // Filter valid offers
    const validOffers = offers.filter(offer => this.isOfferEligible(offer, criteria));
    
    // Create comparisons
    const comparisons: OfferComparison[] = validOffers.map(offer => {
      const platformName = platformNames.get(offer.platformId) || 'Unknown Platform';
      const estimatedCashback = offer.calculateCashback(criteria.spendAmount);
      const score = this.calculateOfferScore(offer, criteria, estimatedCashback);
      
      return {
        offer,
        platformName,
        estimatedCashback,
        rank: 0, // Will be set during sorting
        score,
      };
    });

    // Calculate metrics
    const processingTime = Date.now() - startTime;
    const metrics = this.calculateMetrics(offers, validOffers, comparisons, processingTime);

    // Create comparison result
    const resultId = this.generateComparisonId(criteria);
    return new ComparisonResult(resultId, criteria, comparisons, metrics);
  }

  private isOfferEligible(offer: CashbackOffer, criteria: ComparisonCriteria): boolean {
    // Check if offer is valid
    if (!offer.isValid()) {
      return false;
    }

    // Check category match
    if (criteria.category && !offer.belongsToCategory(criteria.category)) {
      return false;
    }

    // Check merchant match
    if (criteria.merchant && !offer.belongsToMerchant(criteria.merchant)) {
      return false;
    }

    return true;
  }

  private calculateOfferScore(
    offer: CashbackOffer,
    criteria: ComparisonCriteria,
    estimatedCashback: Money,
  ): number {
    // Normalize cashback score (0-1)
    const maxPossibleCashback = criteria.spendAmount.amount;
    const cashbackScore = Math.min(estimatedCashback.amount / maxPossibleCashback, 1);

    // Calculate reliability score based on platform and offer characteristics
    const reliabilityScore = this.calculateReliabilityScore(offer);

    // Calculate popularity score based on offer metadata
    const popularityScore = this.calculatePopularityScore(offer);

    // Calculate validity score based on offer terms
    const validityScore = this.calculateValidityScore(offer);

    // Weighted final score
    return (
      cashbackScore * this.scoring.cashbackWeight +
      reliabilityScore * this.scoring.reliabilityWeight +
      popularityScore * this.scoring.popularityWeight +
      validityScore * this.scoring.validityWeight
    );
  }

  private calculateReliabilityScore(offer: CashbackOffer): number {
    // This would typically consider platform reputation, offer history, etc.
    // For now, return a base score that can be enhanced later
    let score = 0.7; // Base reliability score

    // Boost for featured offers (assuming they're more reliable)
    if (offer.metadata.featured) {
      score += 0.2;
    }

    // Boost for offers with clear terms
    if (offer.terms.description && offer.terms.description.length > 50) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  private calculatePopularityScore(offer: CashbackOffer): number {
    // This would typically consider user engagement, redemption rates, etc.
    // For now, use priority and featured status as proxies
    let score = offer.metadata.priority / 10; // Assuming priority is 1-10

    if (offer.metadata.featured) {
      score += 0.3;
    }

    return Math.min(score, 1.0);
  }

  private calculateValidityScore(offer: CashbackOffer): number {
    const now = new Date();
    let score = 1.0;

    // Reduce score if offer expires soon
    if (offer.terms.validUntil) {
      const timeUntilExpiry = offer.terms.validUntil.getTime() - now.getTime();
      const daysUntilExpiry = timeUntilExpiry / (1000 * 60 * 60 * 24);
      
      if (daysUntilExpiry < 1) {
        score *= 0.5; // Expires within a day
      } else if (daysUntilExpiry < 7) {
        score *= 0.8; // Expires within a week
      }
    }

    // Reduce score if redemption limit is close
    if (offer.terms.maxRedemptions && offer.terms.currentRedemptions) {
      const redemptionRatio = offer.terms.currentRedemptions / offer.terms.maxRedemptions;
      if (redemptionRatio > 0.9) {
        score *= 0.6; // Almost at limit
      } else if (redemptionRatio > 0.7) {
        score *= 0.8; // Getting close to limit
      }
    }

    return score;
  }

  private calculateMetrics(
    allOffers: CashbackOffer[],
    validOffers: CashbackOffer[],
    comparisons: OfferComparison[],
    processingTimeMs: number,
  ) {
    const cashbackRates = comparisons.map(c => c.offer.cashbackRate.percentage);
    const averageCashbackRate = cashbackRates.length > 0 
      ? cashbackRates.reduce((sum, rate) => sum + rate, 0) / cashbackRates.length 
      : 0;
    const bestCashbackRate = cashbackRates.length > 0 ? Math.max(...cashbackRates) : 0;

    return {
      totalOffersEvaluated: allOffers.length,
      validOffersFound: validOffers.length,
      averageCashbackRate,
      bestCashbackRate,
      processingTimeMs,
    };
  }

  private generateComparisonId(criteria: ComparisonCriteria): string {
    const timestamp = Date.now();
    const hash = this.simpleHash(JSON.stringify(criteria));
    return `comp_${timestamp}_${hash}`;
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}
