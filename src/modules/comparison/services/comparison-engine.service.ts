import { Injectable, Logger, Inject } from '@nestjs/common';
import { ComparisonService } from '../../../domain/services/comparison.service';
import { ComparisonResult, ComparisonCriteria } from '../../../domain/entities/comparison-result.entity';
import { CashbackOfferRepository } from '../../../domain/repositories/cashback-offer.repository';
import { CashbackPlatformRepository } from '../../../domain/repositories/cashback-platform.repository';
import { ComparisonResultRepository } from '../../../domain/repositories/comparison-result.repository';
import { PlatformIntegrationStrategy } from '../../../infrastructure/integrations/strategy/platform-integration.strategy';
import { Money } from '../../../domain/value-objects/money.vo';
import {
  CASHBACK_OFFER_REPOSITORY,
  CASHBACK_PLATFORM_REPOSITORY,
  COMPARISON_RESULT_REPOSITORY
} from '../../../infrastructure/repositories/repository.module';

export interface ComparisonRequest {
  spendAmount: number;
  currency?: string;
  category?: string;
  merchant?: string;
  country?: string;
  useCache?: boolean;
  maxCacheAge?: number; // in minutes
}

export interface ComparisonOptions {
  includeInactive?: boolean;
  maxResults?: number;
  minCashbackRate?: number;
  platformIds?: string[];
}

@Injectable()
export class ComparisonEngineService {
  private readonly logger = new Logger(ComparisonEngineService.name);

  constructor(
    private readonly comparisonService: ComparisonService,
    @Inject(CASHBACK_OFFER_REPOSITORY)
    private readonly offerRepository: CashbackOfferRepository,
    @Inject(CASHBACK_PLATFORM_REPOSITORY)
    private readonly platformRepository: CashbackPlatformRepository,
    @Inject(COMPARISON_RESULT_REPOSITORY)
    private readonly comparisonResultRepository: ComparisonResultRepository,
    private readonly platformIntegrationStrategy: PlatformIntegrationStrategy,
  ) {}

  async compareOffers(
    request: ComparisonRequest,
    options: ComparisonOptions = {},
  ): Promise<ComparisonResult> {
    this.logger.log(`Starting comparison for ${request.spendAmount} ${request.currency || 'USD'}`);

    // Create comparison criteria
    const criteria: ComparisonCriteria = {
      spendAmount: new Money(request.spendAmount, request.currency || 'USD'),
      category: request.category,
      merchant: request.merchant,
      country: request.country,
      currency: request.currency || 'USD',
    };

    // Check cache if enabled
    if (request.useCache !== false) {
      const cachedResult = await this.getCachedComparison(criteria, request.maxCacheAge);
      if (cachedResult) {
        this.logger.log('Returning cached comparison result');
        return cachedResult;
      }
    }

    // Fetch offers from database
    const offers = await this.fetchRelevantOffers(criteria, options);
    this.logger.log(`Found ${offers.length} relevant offers`);

    // Get platform names for display
    const platformNames = await this.getPlatformNames(options.platformIds);

    // Perform comparison
    const result = this.comparisonService.compareOffers(criteria, offers, platformNames);

    // Apply result filtering
    const filteredResult = this.applyResultFiltering(result, options);

    // Cache the result
    await this.cacheComparisonResult(filteredResult);

    this.logger.log(`Comparison completed with ${filteredResult.comparisons.length} results`);
    return filteredResult;
  }

  async refreshComparison(comparisonId: string): Promise<ComparisonResult> {
    const existingResult = await this.comparisonResultRepository.findById(comparisonId);
    
    if (!existingResult) {
      throw new Error(`Comparison result not found: ${comparisonId}`);
    }

    // Create new comparison request from existing criteria
    const request: ComparisonRequest = {
      spendAmount: existingResult.criteria.spendAmount.amount,
      currency: existingResult.criteria.spendAmount.currency,
      category: existingResult.criteria.category,
      merchant: existingResult.criteria.merchant,
      country: existingResult.criteria.country,
      useCache: false, // Force fresh data
    };

    return this.compareOffers(request);
  }

  async syncAndCompare(
    request: ComparisonRequest,
    options: ComparisonOptions = {},
  ): Promise<ComparisonResult> {
    this.logger.log('Syncing platform data before comparison');

    // Sync data from external platforms
    const syncOptions = {
      platformIds: options.platformIds,
      maxConcurrency: 2,
      skipHealthCheck: false,
    };

    const syncResult = await this.platformIntegrationStrategy.syncAllPlatforms(syncOptions);
    this.logger.log(`Sync completed: ${syncResult.successfulSyncs}/${syncResult.totalPlatforms} platforms synced`);

    // Perform comparison with fresh data
    return this.compareOffers({ ...request, useCache: false }, options);
  }

  async getComparisonHistory(
    criteria: Partial<ComparisonCriteria>,
    limit: number = 10,
  ): Promise<ComparisonResult[]> {
    return this.comparisonResultRepository.findAll({
      limit,
      notExpired: true,
    });
  }

  async cleanupExpiredComparisons(): Promise<number> {
    const deletedCount = await this.comparisonResultRepository.deleteExpired();
    this.logger.log(`Cleaned up ${deletedCount} expired comparison results`);
    return deletedCount;
  }

  private async getCachedComparison(
    criteria: ComparisonCriteria,
    maxCacheAge?: number,
  ): Promise<ComparisonResult | null> {
    try {
      const cachedResult = await this.comparisonResultRepository.findByCriteria(criteria);
      
      if (!cachedResult || !cachedResult.isValid()) {
        return null;
      }

      // Check cache age if specified
      if (maxCacheAge) {
        const ageInMinutes = (Date.now() - cachedResult.createdAt.getTime()) / (1000 * 60);
        if (ageInMinutes > maxCacheAge) {
          return null;
        }
      }

      return cachedResult;
    } catch (error) {
      this.logger.warn('Failed to retrieve cached comparison', error);
      return null;
    }
  }

  private async fetchRelevantOffers(
    criteria: ComparisonCriteria,
    options: ComparisonOptions,
  ) {
    const findOptions = {
      status: options.includeInactive ? undefined : 'active' as any,
      category: criteria.category,
      merchant: criteria.merchant,
      validAt: new Date(),
      platformId: options.platformIds?.length ? options.platformIds[0] : undefined,
      limit: options.maxResults || 1000,
      sortBy: 'cashbackRate' as any,
      sortOrder: 'desc' as any,
    };

    let offers = await this.offerRepository.findAll(findOptions);

    // Filter by platform IDs if specified
    if (options.platformIds?.length) {
      offers = offers.filter(offer => options.platformIds!.includes(offer.platformId));
    }

    // Filter by minimum cashback rate if specified
    if (options.minCashbackRate) {
      offers = offers.filter(offer => offer.cashbackRate.percentage >= options.minCashbackRate!);
    }

    return offers;
  }

  private async getPlatformNames(platformIds?: string[]): Promise<Map<string, string>> {
    const platformNames = new Map<string, string>();

    try {
      const platforms = platformIds?.length
        ? await Promise.all(platformIds.map(id => this.platformRepository.findById(id)))
        : await this.platformRepository.findAll();

      platforms
        .filter(platform => platform !== null)
        .forEach(platform => {
          platformNames.set(platform!.id, platform!.name);
        });
    } catch (error) {
      this.logger.warn('Failed to fetch platform names', error);
    }

    return platformNames;
  }

  private applyResultFiltering(
    result: ComparisonResult,
    options: ComparisonOptions,
  ): ComparisonResult {
    let filteredComparisons = result.comparisons;

    // Apply max results limit
    if (options.maxResults && filteredComparisons.length > options.maxResults) {
      filteredComparisons = filteredComparisons.slice(0, options.maxResults);
    }

    // Create new result with filtered comparisons
    if (filteredComparisons.length !== result.comparisons.length) {
      const newMetrics = {
        ...result.metrics,
        validOffersFound: filteredComparisons.length,
      };

      return new ComparisonResult(
        result.id,
        result.criteria,
        filteredComparisons,
        newMetrics,
        result.status,
        result.createdAt,
        result.expiresAt,
      );
    }

    return result;
  }

  private async cacheComparisonResult(result: ComparisonResult): Promise<void> {
    try {
      await this.comparisonResultRepository.save(result);
    } catch (error) {
      this.logger.warn('Failed to cache comparison result', error);
      // Don't throw error as caching is not critical
    }
  }
}
