"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Money = void 0;
class Money {
    _amount;
    _currency;
    constructor(_amount, _currency = 'USD') {
        this._amount = _amount;
        this._currency = _currency;
        this.validateAmount();
        this.validateCurrency();
    }
    get amount() {
        return this._amount;
    }
    get currency() {
        return this._currency;
    }
    validateAmount() {
        if (this._amount < 0) {
            throw new Error('Amount must be non-negative');
        }
        if (!Number.isFinite(this._amount)) {
            throw new Error('Amount must be a finite number');
        }
    }
    validateCurrency() {
        if (!this._currency || this._currency.length !== 3) {
            throw new Error('Currency must be a valid 3-letter code');
        }
    }
    add(other) {
        this.ensureSameCurrency(other);
        return new Money(this._amount + other._amount, this._currency);
    }
    subtract(other) {
        this.ensureSameCurrency(other);
        return new Money(this._amount - other._amount, this._currency);
    }
    multiply(factor) {
        return new Money(this._amount * factor, this._currency);
    }
    divide(divisor) {
        if (divisor === 0) {
            throw new Error('Cannot divide by zero');
        }
        return new Money(this._amount / divisor, this._currency);
    }
    isGreaterThan(other) {
        this.ensureSameCurrency(other);
        return this._amount > other._amount;
    }
    isLessThan(other) {
        this.ensureSameCurrency(other);
        return this._amount < other._amount;
    }
    equals(other) {
        return this._amount === other._amount && this._currency === other._currency;
    }
    ensureSameCurrency(other) {
        if (this._currency !== other._currency) {
            throw new Error(`Currency mismatch: ${this._currency} vs ${other._currency}`);
        }
    }
    toString() {
        return `${this._currency} ${this._amount.toFixed(2)}`;
    }
    toJSON() {
        return {
            amount: this._amount,
            currency: this._currency,
        };
    }
}
exports.Money = Money;
//# sourceMappingURL=money.vo.js.map