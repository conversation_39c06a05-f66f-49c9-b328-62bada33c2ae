import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
export interface CacheOptions {
    ttl?: number;
    namespace?: string;
}
export declare class CacheService {
    private cacheManager;
    private configService;
    private readonly logger;
    constructor(cacheManager: Cache, configService: ConfigService);
    get<T>(key: string, namespace?: string): Promise<T | null>;
    set<T>(key: string, value: T, options?: CacheOptions): Promise<void>;
    del(key: string, namespace?: string): Promise<void>;
    mget<T>(keys: string[], namespace?: string): Promise<(T | null)[]>;
    mset<T>(keyValuePairs: Array<{
        key: string;
        value: T;
    }>, options?: CacheOptions): Promise<void>;
    mdel(keys: string[], namespace?: string): Promise<void>;
    clear(namespace?: string): Promise<void>;
    exists(key: string, namespace?: string): Promise<boolean>;
    getOrSet<T>(key: string, factory: () => Promise<T>, options?: CacheOptions): Promise<T>;
    wrap<T>(key: string, factory: () => Promise<T>, options?: CacheOptions): Promise<T>;
    private buildKey;
    private getDefaultTtl;
    getStats(): Promise<{
        hits: number;
        misses: number;
        keys: number;
        hitRate: number;
    }>;
    healthCheck(): Promise<boolean>;
}
