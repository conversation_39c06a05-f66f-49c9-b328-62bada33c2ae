"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformReportDto = exports.TrendDataDto = exports.ComparisonAnalyticsDto = exports.PlatformStatsDto = exports.MerchantStatsDto = exports.CategoryStatsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CategoryStatsDto {
    category;
    comparisonCount;
    averageCashbackRate;
    totalOffers;
}
exports.CategoryStatsDto = CategoryStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Category name',
        example: 'shopping',
    }),
    __metadata("design:type", String)
], CategoryStatsDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of comparisons in this category',
        example: 150,
    }),
    __metadata("design:type", Number)
], CategoryStatsDto.prototype, "comparisonCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average cashback rate for this category',
        example: 4.2,
    }),
    __metadata("design:type", Number)
], CategoryStatsDto.prototype, "averageCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total offers in this category',
        example: 450,
    }),
    __metadata("design:type", Number)
], CategoryStatsDto.prototype, "totalOffers", void 0);
class MerchantStatsDto {
    merchant;
    comparisonCount;
    averageCashbackRate;
    totalOffers;
}
exports.MerchantStatsDto = MerchantStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Merchant name',
        example: 'Amazon',
    }),
    __metadata("design:type", String)
], MerchantStatsDto.prototype, "merchant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of comparisons for this merchant',
        example: 75,
    }),
    __metadata("design:type", Number)
], MerchantStatsDto.prototype, "comparisonCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average cashback rate for this merchant',
        example: 3.8,
    }),
    __metadata("design:type", Number)
], MerchantStatsDto.prototype, "averageCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total offers for this merchant',
        example: 125,
    }),
    __metadata("design:type", Number)
], MerchantStatsDto.prototype, "totalOffers", void 0);
class PlatformStatsDto {
    platformId;
    platformName;
    totalOffers;
    averageCashbackRate;
    winRate;
    averageRank;
}
exports.PlatformStatsDto = PlatformStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Platform ID',
        example: 'platform1',
    }),
    __metadata("design:type", String)
], PlatformStatsDto.prototype, "platformId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Platform name',
        example: 'Sample Cashback Platform',
    }),
    __metadata("design:type", String)
], PlatformStatsDto.prototype, "platformName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total offers from this platform',
        example: 200,
    }),
    __metadata("design:type", Number)
], PlatformStatsDto.prototype, "totalOffers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average cashback rate for this platform',
        example: 4.5,
    }),
    __metadata("design:type", Number)
], PlatformStatsDto.prototype, "averageCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Win rate percentage (how often this platform has the best offer)',
        example: 35.2,
    }),
    __metadata("design:type", Number)
], PlatformStatsDto.prototype, "winRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average rank in comparisons',
        example: 2.3,
    }),
    __metadata("design:type", Number)
], PlatformStatsDto.prototype, "averageRank", void 0);
class ComparisonAnalyticsDto {
    totalComparisons;
    averageOffersPerComparison;
    topCategories;
    topMerchants;
    platformPerformance;
    averageProcessingTime;
    cacheHitRate;
}
exports.ComparisonAnalyticsDto = ComparisonAnalyticsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of comparisons',
        example: 1250,
    }),
    __metadata("design:type", Number)
], ComparisonAnalyticsDto.prototype, "totalComparisons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average number of offers per comparison',
        example: 8.5,
    }),
    __metadata("design:type", Number)
], ComparisonAnalyticsDto.prototype, "averageOffersPerComparison", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Top categories by comparison count',
        type: [CategoryStatsDto],
    }),
    __metadata("design:type", Array)
], ComparisonAnalyticsDto.prototype, "topCategories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Top merchants by comparison count',
        type: [MerchantStatsDto],
    }),
    __metadata("design:type", Array)
], ComparisonAnalyticsDto.prototype, "topMerchants", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Platform performance statistics',
        type: [PlatformStatsDto],
    }),
    __metadata("design:type", Array)
], ComparisonAnalyticsDto.prototype, "platformPerformance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average processing time in milliseconds',
        example: 185,
    }),
    __metadata("design:type", Number)
], ComparisonAnalyticsDto.prototype, "averageProcessingTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cache hit rate percentage',
        example: 65.5,
    }),
    __metadata("design:type", Number)
], ComparisonAnalyticsDto.prototype, "cacheHitRate", void 0);
class TrendDataDto {
    date;
    comparisons;
    averageCashbackRate;
    topCategory;
}
exports.TrendDataDto = TrendDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date or period',
        example: '2024-01-01',
    }),
    __metadata("design:type", String)
], TrendDataDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of comparisons in this period',
        example: 45,
    }),
    __metadata("design:type", Number)
], TrendDataDto.prototype, "comparisons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average cashback rate in this period',
        example: 4.1,
    }),
    __metadata("design:type", Number)
], TrendDataDto.prototype, "averageCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Top category in this period',
        example: 'shopping',
    }),
    __metadata("design:type", String)
], TrendDataDto.prototype, "topCategory", void 0);
class PlatformReportDto {
    totalComparisons;
    winRate;
    averageRank;
    averageCashbackRate;
    topCategories;
    recentPerformance;
}
exports.PlatformReportDto = PlatformReportDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total comparisons involving this platform',
        example: 320,
    }),
    __metadata("design:type", Number)
], PlatformReportDto.prototype, "totalComparisons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Win rate percentage',
        example: 28.5,
    }),
    __metadata("design:type", Number)
], PlatformReportDto.prototype, "winRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average rank in comparisons',
        example: 2.8,
    }),
    __metadata("design:type", Number)
], PlatformReportDto.prototype, "averageRank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average cashback rate',
        example: 4.2,
    }),
    __metadata("design:type", Number)
], PlatformReportDto.prototype, "averageCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Top categories for this platform',
        example: ['shopping', 'travel', 'dining'],
    }),
    __metadata("design:type", Array)
], PlatformReportDto.prototype, "topCategories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recent performance trend',
        type: [TrendDataDto],
    }),
    __metadata("design:type", Array)
], PlatformReportDto.prototype, "recentPerformance", void 0);
//# sourceMappingURL=analytics-response.dto.js.map