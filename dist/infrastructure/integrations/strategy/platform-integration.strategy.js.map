{"version": 3, "file": "platform-integration.strategy.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/integrations/strategy/platform-integration.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAqB7C,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IACrB,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IACtD,YAAY,GAAG,IAAI,GAAG,EAA+B,CAAC;IAEvE,mBAAmB,CAAC,WAAgC;QAClD,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;QAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,UAAU,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,qBAAqB,CAAC,UAAkB;QACtC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,UAAU,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,cAAc,CAAC,UAAkB;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,wBAAwB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAA+B,EAAE;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC3E,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,WAAW,CAAC,MAAM,+BAA+B,cAAc,EAAE,CAAC,CAAC;QAE9G,MAAM,OAAO,GAAG,IAAI,GAAG,EAAsB,CAAC;QAC9C,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,WAAW,GAAG,CAAC,CAAC;QAGpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC;YAC5D,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,CAAC;YACvD,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;YAE5F,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAE7D,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACrC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;gBAEhC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBAClC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;oBACtC,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;wBACzB,eAAe,EAAE,CAAC;oBACpB,CAAC;yBAAM,CAAC;wBACN,WAAW,EAAE,CAAC;oBAChB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,WAAW,EAAE,CAAC;oBACd,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;wBACtB,OAAO,EAAE,KAAK;wBACd,eAAe,EAAE,CAAC;wBAClB,WAAW,EAAE,CAAC;wBACd,aAAa,EAAE,CAAC;wBAChB,aAAa,EAAE,CAAC;wBAChB,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,eAAe,CAAC;wBACnD,YAAY,EAAE,CAAC;qBAChB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,eAAe,gBAAgB,WAAW,YAAY,aAAa,UAAU,CAAC,CAAC;QAExH,OAAO;YACL,cAAc,EAAE,WAAW,CAAC,MAAM;YAClC,eAAe;YACf,WAAW;YACX,OAAO;YACP,aAAa;SACd,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,UAA+B,EAAE;QAC5E,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEpD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;QAGD,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,YAAY,UAAU,oBAAoB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,OAAO,WAAW,CAAC,UAAU,EAAE,CAAC;QAClC,CAAC;aAAM,CAAC;YAGN,OAAO,WAAW,CAAC,UAAU,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,2BAA2B;QAC/B,MAAM,OAAO,GAAG,IAAI,GAAG,EAA2B,CAAC;QACnD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YACtD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC/C,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;gBAChG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,OAAO,GAAG,IAAI,GAAG,EAA2B,CAAC;QACnD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YACtD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBACjE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC5G,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,OAAO,GAAG,IAAI,GAAG,EAA2B,CAAC;QACnD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YACtD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBACjE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC5G,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,uBAAuB;QAC3B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAmB,CAAC;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YACtD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC/C,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAC7D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC5F,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEpD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,WAAW,CAAC,eAAe,EAAE,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,OAAO,GAAG,IAAI,GAAG,EAA4B,CAAC;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE/C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YACtD,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,eAAe,EAAE,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,YAAY,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,WAAW,CAAC,aAAa,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAA;AA/MY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;GACA,2BAA2B,CA+MvC"}