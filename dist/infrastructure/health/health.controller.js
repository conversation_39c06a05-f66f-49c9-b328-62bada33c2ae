"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const terminus_1 = require("@nestjs/terminus");
const database_health_indicator_1 = require("./indicators/database-health.indicator");
const cache_health_indicator_1 = require("./indicators/cache-health.indicator");
const platform_health_indicator_1 = require("./indicators/platform-health.indicator");
let HealthController = class HealthController {
    health;
    databaseHealthIndicator;
    cacheHealthIndicator;
    platformHealthIndicator;
    constructor(health, databaseHealthIndicator, cacheHealthIndicator, platformHealthIndicator) {
        this.health = health;
        this.databaseHealthIndicator = databaseHealthIndicator;
        this.cacheHealthIndicator = cacheHealthIndicator;
        this.platformHealthIndicator = platformHealthIndicator;
    }
    check() {
        return this.health.check([
            () => this.databaseHealthIndicator.isHealthy('database'),
            () => this.cacheHealthIndicator.isHealthy('cache'),
            () => this.platformHealthIndicator.isHealthy('platforms'),
        ]);
    }
    checkDatabase() {
        return this.health.check([
            () => this.databaseHealthIndicator.isHealthy('database'),
        ]);
    }
    checkCache() {
        return this.health.check([
            () => this.cacheHealthIndicator.isHealthy('cache'),
        ]);
    }
    checkPlatforms() {
        return this.health.check([
            () => this.platformHealthIndicator.isHealthy('platforms'),
        ]);
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check',
        description: 'Check the health status of all system components',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health check successful',
    }),
    (0, swagger_1.ApiResponse)({
        status: 503,
        description: 'One or more health checks failed',
    }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('database'),
    (0, swagger_1.ApiOperation)({
        summary: 'Database health check',
        description: 'Check the health status of the database connection',
    }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "checkDatabase", null);
__decorate([
    (0, common_1.Get)('cache'),
    (0, swagger_1.ApiOperation)({
        summary: 'Cache health check',
        description: 'Check the health status of the cache system',
    }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "checkCache", null);
__decorate([
    (0, common_1.Get)('platforms'),
    (0, swagger_1.ApiOperation)({
        summary: 'Platform integrations health check',
        description: 'Check the health status of external platform integrations',
    }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "checkPlatforms", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)('Health'),
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [terminus_1.HealthCheckService,
        database_health_indicator_1.DatabaseHealthIndicator,
        cache_health_indicator_1.CacheHealthIndicator,
        platform_health_indicator_1.PlatformHealthIndicator])
], HealthController);
//# sourceMappingURL=health.controller.js.map