import { CashbackOffer } from '../entities/cashback-offer.entity';
import { ComparisonResult, ComparisonCriteria } from '../entities/comparison-result.entity';
export interface ComparisonScoring {
    cashbackWeight: number;
    reliabilityWeight: number;
    popularityWeight: number;
    validityWeight: number;
}
export declare class ComparisonService {
    private readonly scoring;
    private readonly defaultScoring;
    constructor(scoring?: ComparisonScoring);
    private validateScoring;
    compareOffers(criteria: ComparisonCriteria, offers: CashbackOffer[], platformNames: Map<string, string>): ComparisonResult;
    private isOfferEligible;
    private calculateOfferScore;
    private calculateReliabilityScore;
    private calculatePopularityScore;
    private calculateValidityScore;
    private calculateMetrics;
    private generateComparisonId;
    private simpleHash;
}
