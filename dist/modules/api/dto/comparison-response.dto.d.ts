export declare class MoneyDto {
    amount: number;
    currency: string;
}
export declare class CashbackRateDto {
    percentage: number;
    maxAmount?: number;
    minSpend?: number;
}
export declare class OfferTermsDto {
    description: string;
    exclusions?: string[];
    validFrom: string;
    validUntil?: string;
    maxRedemptions?: number;
    currentRedemptions?: number;
}
export declare class OfferMetadataDto {
    category: string;
    subcategory?: string;
    merchant: string;
    tags: string[];
    featured: boolean;
    priority: number;
}
export declare class CashbackOfferDto {
    id: string;
    platformId: string;
    title: string;
    cashbackRate: CashbackRateDto;
    terms: OfferTermsDto;
    metadata: OfferMetadataDto;
    status: string;
    createdAt: string;
    updatedAt: string;
}
export declare class OfferComparisonDto {
    offer: CashbackOfferDto;
    platformName: string;
    estimatedCashback: MoneyDto;
    rank: number;
    score: number;
}
export declare class ComparisonMetricsDto {
    totalOffersEvaluated: number;
    validOffersFound: number;
    averageCashbackRate: number;
    bestCashbackRate: number;
    processingTimeMs: number;
}
export declare class ComparisonCriteriaDto {
    spendAmount: MoneyDto;
    category?: string;
    merchant?: string;
    country?: string;
    currency?: string;
}
export declare class ComparisonResultDto {
    id: string;
    criteria: ComparisonCriteriaDto;
    comparisons: OfferComparisonDto[];
    metrics: ComparisonMetricsDto;
    status: string;
    createdAt: string;
    expiresAt: string;
}
