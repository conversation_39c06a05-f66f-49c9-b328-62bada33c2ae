# Development Environment Configuration

# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/cashback-comparison-dev

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
REDIS_KEY_PREFIX=cashback:dev:

# Cache Configuration
CACHE_COMPARISON_TTL=300
CACHE_OFFERS_TTL=1800
CACHE_PLATFORMS_TTL=3600
CACHE_ANALYTICS_TTL=900
CACHE_HEALTH_TTL=60
CACHE_MAX_COMPARISONS=1000
CACHE_MAX_OFFERS=5000
CACHE_MAX_PLATFORMS=50
CACHE_MAX_ANALYTICS=100

# Rate Limiting (more lenient for development)
THROTTLE_TTL=60
THROTTLE_LIMIT=1000

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app-dev.log

# External APIs (use test/sandbox keys)
# SAMPLE_PLATFORM_API_KEY=test_api_key_here
