"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonMapper = void 0;
const common_1 = require("@nestjs/common");
let ComparisonMapper = class ComparisonMapper {
    mapComparisonResultToDto(result) {
        return {
            id: result.id,
            criteria: this.mapComparisonCriteriaToDto(result.criteria),
            comparisons: result.comparisons.map(comp => this.mapOfferComparisonToDto(comp)),
            metrics: this.mapComparisonMetricsToDto(result.metrics),
            status: result.status,
            createdAt: result.createdAt.toISOString(),
            expiresAt: result.expiresAt.toISOString(),
        };
    }
    mapOfferComparisonToDto(comparison) {
        return {
            offer: this.mapCashbackOfferToDto(comparison.offer),
            platformName: comparison.platformName,
            estimatedCashback: this.mapMoneyToDto(comparison.estimatedCashback),
            rank: comparison.rank,
            score: comparison.score,
        };
    }
    mapCashbackOfferToDto(offer) {
        return {
            id: offer.id,
            platformId: offer.platformId,
            title: offer.title,
            cashbackRate: this.mapCashbackRateToDto(offer.cashbackRate),
            terms: this.mapOfferTermsToDto(offer.terms),
            metadata: this.mapOfferMetadataToDto(offer.metadata),
            status: offer.status,
            createdAt: offer.createdAt.toISOString(),
            updatedAt: offer.updatedAt.toISOString(),
        };
    }
    mapMoneyToDto(money) {
        return {
            amount: money.amount,
            currency: money.currency,
        };
    }
    mapCashbackRateToDto(rate) {
        return {
            percentage: rate.percentage,
            maxAmount: rate.maxAmount,
            minSpend: rate.minSpend,
        };
    }
    mapOfferTermsToDto(terms) {
        return {
            description: terms.description,
            exclusions: terms.exclusions,
            validFrom: terms.validFrom.toISOString(),
            validUntil: terms.validUntil?.toISOString(),
            maxRedemptions: terms.maxRedemptions,
            currentRedemptions: terms.currentRedemptions,
        };
    }
    mapOfferMetadataToDto(metadata) {
        return {
            category: metadata.category,
            subcategory: metadata.subcategory,
            merchant: metadata.merchant,
            tags: metadata.tags,
            featured: metadata.featured,
            priority: metadata.priority,
        };
    }
    mapComparisonMetricsToDto(metrics) {
        return {
            totalOffersEvaluated: metrics.totalOffersEvaluated,
            validOffersFound: metrics.validOffersFound,
            averageCashbackRate: metrics.averageCashbackRate,
            bestCashbackRate: metrics.bestCashbackRate,
            processingTimeMs: metrics.processingTimeMs,
        };
    }
    mapComparisonCriteriaToDto(criteria) {
        return {
            spendAmount: this.mapMoneyToDto(criteria.spendAmount),
            category: criteria.category,
            merchant: criteria.merchant,
            country: criteria.country,
            currency: criteria.currency,
        };
    }
};
exports.ComparisonMapper = ComparisonMapper;
exports.ComparisonMapper = ComparisonMapper = __decorate([
    (0, common_1.Injectable)()
], ComparisonMapper);
//# sourceMappingURL=comparison.mapper.js.map