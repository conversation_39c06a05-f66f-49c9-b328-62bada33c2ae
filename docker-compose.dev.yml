version: '3.8'

services:
  # Development application with hot reload
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: cashback-comparison-app-dev
    restart: unless-stopped
    ports:
      - "${PORT:-3000}:3000"
      - "9229:9229" # Debug port
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/cashback-comparison-dev
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mongo
      - redis
    networks:
      - cashback-network
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    command: npm run start:dev

  # MongoDB database
  mongo:
    image: mongo:6.0
    container_name: cashback-comparison-mongo-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=cashback-comparison-dev
    volumes:
      - mongo_dev_data:/data/db
    networks:
      - cashback-network

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: cashback-comparison-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    networks:
      - cashback-network

  # MongoDB Express
  mongo-express:
    image: mongo-express:latest
    container_name: cashback-comparison-mongo-express-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongo
    networks:
      - cashback-network

  # Redis Commander
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: cashback-comparison-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - cashback-network

volumes:
  mongo_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  cashback-network:
    driver: bridge
