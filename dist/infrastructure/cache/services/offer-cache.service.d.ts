import { CacheService } from './cache.service';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
export declare class OfferCacheService {
    private readonly cacheService;
    private readonly logger;
    private readonly namespace;
    constructor(cacheService: CacheService);
    getOffer(id: string): Promise<CashbackOffer | null>;
    setOffer(offer: CashbackOffer): Promise<void>;
    getOffers(ids: string[]): Promise<(CashbackOffer | null)[]>;
    setOffers(offers: CashbackOffer[]): Promise<void>;
    getOffersByPlatform(platformId: string): Promise<CashbackOffer[]>;
    setOffersByPlatform(platformId: string, offers: CashbackOffer[]): Promise<void>;
    getOffersByCategory(category: string): Promise<CashbackOffer[]>;
    setOffersByCategory(category: string, offers: CashbackOffer[]): Promise<void>;
    getOffersByMerchant(merchant: string): Promise<CashbackOffer[]>;
    setOffersByMerchant(merchant: string, offers: CashbackOffer[]): Promise<void>;
    getFeaturedOffers(): Promise<CashbackOffer[]>;
    setFeaturedOffers(offers: CashbackOffer[]): Promise<void>;
    invalidateOffer(id: string): Promise<void>;
    invalidateOffers(ids: string[]): Promise<void>;
    invalidatePlatformOffers(platformId: string): Promise<void>;
    invalidateCategoryOffers(category: string): Promise<void>;
    invalidateMerchantOffers(merchant: string): Promise<void>;
    invalidateExpiredOffers(): Promise<void>;
    refreshOfferCache(platformId: string, offerFactory: () => Promise<CashbackOffer[]>): Promise<void>;
    private calculateOfferTtl;
    private groupOffersByCategory;
    private groupOffersByMerchant;
    getOfferStats(): Promise<{
        totalCached: number;
        byPlatform: Record<string, number>;
        byCategory: Record<string, number>;
        expiringSoon: number;
    }>;
}
