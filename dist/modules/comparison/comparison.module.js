"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonModule = void 0;
const common_1 = require("@nestjs/common");
const comparison_engine_service_1 = require("./services/comparison-engine.service");
const comparison_analytics_service_1 = require("./services/comparison-analytics.service");
const comparison_service_1 = require("../../domain/services/comparison.service");
const platform_integration_module_1 = require("../../infrastructure/integrations/platform-integration.module");
let ComparisonModule = class ComparisonModule {
};
exports.ComparisonModule = ComparisonModule;
exports.ComparisonModule = ComparisonModule = __decorate([
    (0, common_1.Module)({
        imports: [platform_integration_module_1.PlatformIntegrationModule],
        providers: [
            comparison_service_1.ComparisonService,
            comparison_engine_service_1.ComparisonEngineService,
            comparison_analytics_service_1.ComparisonAnalyticsService,
        ],
        exports: [
            comparison_engine_service_1.ComparisonEngineService,
            comparison_analytics_service_1.ComparisonAnalyticsService,
            comparison_service_1.ComparisonService,
        ],
    })
], ComparisonModule);
//# sourceMappingURL=comparison.module.js.map