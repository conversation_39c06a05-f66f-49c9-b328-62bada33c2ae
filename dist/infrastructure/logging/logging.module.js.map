{"version": 3, "file": "logging.module.js", "sourceRoot": "", "sources": ["../../../src/infrastructure/logging/logging.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAgD;AAChD,+CAA6C;AAC7C,2CAA6D;AAC7D,mCAAmC;AACnC,6BAA6B;AAuHtB,IAAM,aAAa,GAAnB,MAAM,aAAa;CAAG,CAAA;AAAhB,sCAAa;wBAAb,aAAa;IArHzB,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,4BAAa,CAAC,YAAY,CAAC;gBACzB,OAAO,EAAE,CAAC,qBAAY,CAAC;gBACvB,UAAU,EAAE,CAAC,aAA4B,EAAE,EAAE;oBAC3C,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAS,mBAAmB,EAAE,MAAM,CAAC,CAAC;oBACxE,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAS,sBAAsB,EAAE,cAAc,CAAC,CAAC;oBACtF,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAS,aAAa,EAAE,aAAa,CAAC,CAAC;oBAExE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;oBAGzC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CACzC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;wBACvB,MAAM,EAAE,yBAAyB;qBAClC,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,EACrB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE;wBAC/E,MAAM,QAAQ,GAAG;4BACf,SAAS;4BACT,KAAK;4BACL,OAAO;4BACP,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;4BAC3B,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,CAAC;4BACvB,GAAG,IAAI;yBACR,CAAC;wBACF,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBAClC,CAAC,CAAC,CACH,CAAC;oBAEF,MAAM,UAAU,GAAwB,EAAE,CAAC;oBAG3C,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;wBAC9B,UAAU,CAAC,IAAI,CACb,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;4BAC7B,KAAK,EAAE,QAAQ;4BACf,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;gCACvB,MAAM,EAAE,cAAc;6BACvB,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;gCAC/D,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gCAClD,OAAO,GAAG,SAAS,IAAI,KAAK,GAAG,UAAU,KAAK,OAAO,EAAE,CAAC;4BAC1D,CAAC,CAAC,CACH;yBACF,CAAC,CACH,CAAC;oBACJ,CAAC;oBAGD,UAAU,CAAC,IAAI,CAEb,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;wBAC1B,QAAQ,EAAE,WAAW;wBACrB,KAAK,EAAE,QAAQ;wBACf,MAAM,EAAE,YAAY;wBACpB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;wBACzB,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,IAAI;qBACf,CAAC,EAEF,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;wBAC1B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;wBACxC,KAAK,EAAE,OAAO;wBACd,MAAM,EAAE,YAAY;wBACpB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;wBACzB,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,IAAI;qBACf,CAAC,CACH,CAAC;oBAGF,IAAI,OAAO,KAAK,YAAY,EAAE,CAAC;wBAC7B,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAS,sBAAsB,CAAC,CAAC;wBACvE,IAAI,YAAY,EAAE,CAAC;4BACjB,UAAU,CAAC,IAAI,CACb,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;gCAC1B,IAAI,EAAE,YAAY;gCAClB,IAAI,EAAE,GAAG;gCACT,IAAI,EAAE,OAAO;gCACb,GAAG,EAAE,IAAI;gCACT,KAAK,EAAE,OAAO;gCACd,MAAM,EAAE,YAAY;6BACrB,CAAC,CACH,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,OAAO;wBACL,KAAK,EAAE,QAAQ;wBACf,MAAM,EAAE,YAAY;wBACpB,UAAU;wBAEV,iBAAiB,EAAE;4BACjB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;gCAC1B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;gCAC7C,MAAM,EAAE,YAAY;6BACrB,CAAC;yBACH;wBACD,iBAAiB,EAAE;4BACjB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;gCAC1B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;gCAC7C,MAAM,EAAE,YAAY;6BACrB,CAAC;yBACH;wBACD,WAAW,EAAE,KAAK;qBACnB,CAAC;gBACJ,CAAC;gBACD,MAAM,EAAE,CAAC,sBAAa,CAAC;aACxB,CAAC;SACH;QACD,OAAO,EAAE,CAAC,4BAAa,CAAC;KACzB,CAAC;GACW,aAAa,CAAG"}