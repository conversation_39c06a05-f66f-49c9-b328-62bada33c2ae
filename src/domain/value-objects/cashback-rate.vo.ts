export class CashbackRate {
  constructor(
    private readonly _percentage: number,
    private readonly _maxAmount?: number,
    private readonly _minSpend?: number,
  ) {
    this.validatePercentage();
    this.validateMaxAmount();
    this.validateMinSpend();
  }

  get percentage(): number {
    return this._percentage;
  }

  get maxAmount(): number | undefined {
    return this._maxAmount;
  }

  get minSpend(): number | undefined {
    return this._minSpend;
  }

  private validatePercentage(): void {
    if (this._percentage < 0 || this._percentage > 100) {
      throw new Error('Cashback percentage must be between 0 and 100');
    }
  }

  private validateMaxAmount(): void {
    if (this._maxAmount !== undefined && this._maxAmount < 0) {
      throw new Error('Max amount must be positive');
    }
  }

  private validateMinSpend(): void {
    if (this._minSpend !== undefined && this._minSpend < 0) {
      throw new Error('Min spend must be positive');
    }
  }

  calculateCashback(spendAmount: number): number {
    if (this._minSpend && spendAmount < this._minSpend) {
      return 0;
    }

    const cashback = (spendAmount * this._percentage) / 100;
    
    if (this._maxAmount && cashback > this._maxAmount) {
      return this._maxAmount;
    }

    return cashback;
  }

  equals(other: CashbackRate): boolean {
    return (
      this._percentage === other._percentage &&
      this._maxAmount === other._maxAmount &&
      this._minSpend === other._minSpend
    );
  }

  toString(): string {
    let result = `${this._percentage}%`;
    if (this._maxAmount) {
      result += ` (max $${this._maxAmount})`;
    }
    if (this._minSpend) {
      result += ` (min spend $${this._minSpend})`;
    }
    return result;
  }
}
