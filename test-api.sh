#!/bin/bash

# Test script for Cashback Comparison Platform API
# This script tests the main endpoints to ensure the application is working correctly

BASE_URL="http://localhost:3000/api/v1"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🧪 Testing Cashback Comparison Platform API${NC}"
echo "=================================================="

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json -X "$method" -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} (Status: $status_code)"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "Response:"
        cat /tmp/response.json
        echo ""
        return 1
    fi
}

# Test counter
total_tests=0
passed_tests=0

# Test 1: Basic API endpoint
total_tests=$((total_tests + 1))
if test_endpoint "GET" "" "" "200" "Basic API endpoint"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 2: Database health check
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/health/database" "" "200" "Database health check"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 3: Cache health check
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/health/cache" "" "200" "Cache health check"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 4: Create comparison
total_tests=$((total_tests + 1))
comparison_data='{"spendAmount": 100, "category": "electronics"}'
if test_endpoint "POST" "/comparisons" "$comparison_data" "201" "Create comparison"; then
    passed_tests=$((passed_tests + 1))
    # Extract comparison ID for next test
    comparison_id=$(cat /tmp/response.json | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
fi

# Test 5: Get comparison (if previous test passed) - expecting 501 Not Implemented
if [ ! -z "$comparison_id" ]; then
    total_tests=$((total_tests + 1))
    if test_endpoint "GET" "/comparisons/$comparison_id" "" "501" "Get comparison by ID (not implemented)"; then
        passed_tests=$((passed_tests + 1))
    fi
fi

# Test 6: List comparisons
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/comparisons" "" "200" "List comparisons"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 7: Analytics overview
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/analytics/overview" "" "200" "Analytics overview"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 8: Analytics trends with parameters
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/analytics/trends?days=30" "" "200" "Analytics trends with parameters"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 9: Top categories
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/analytics/categories/top" "" "200" "Top categories"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 10: Invalid endpoint (should return 404)
total_tests=$((total_tests + 1))
if test_endpoint "GET" "/invalid-endpoint" "" "404" "Invalid endpoint (404 test)"; then
    passed_tests=$((passed_tests + 1))
fi

# Test 11: Invalid comparison data (should return 400)
total_tests=$((total_tests + 1))
invalid_data='{"invalidField": "test"}'
if test_endpoint "POST" "/comparisons" "$invalid_data" "400" "Invalid comparison data (400 test)"; then
    passed_tests=$((passed_tests + 1))
fi

echo ""
echo "=================================================="
echo -e "${YELLOW}📊 Test Results${NC}"
echo "=================================================="
echo "Total tests: $total_tests"
echo -e "Passed: ${GREEN}$passed_tests${NC}"
echo -e "Failed: ${RED}$((total_tests - passed_tests))${NC}"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed.${NC}"
    exit 1
fi
