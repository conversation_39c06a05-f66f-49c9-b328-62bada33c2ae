import { Injectable } from '@nestjs/common';
import { BasePlatformIntegration } from '../base/base-platform-integration';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform, PlatformConfiguration, PlatformMetadata } from '../../../domain/entities/cashback-platform.entity';
import { CashbackRate } from '../../../domain/value-objects/cashback-rate.vo';
import { PlatformStatus, OfferStatus } from '../../../domain/enums/platform-status.enum';

interface SampleApiOffer {
  id: string;
  title: string;
  description: string;
  cashback_percentage: number;
  max_cashback?: number;
  min_spend?: number;
  merchant: string;
  category: string;
  tags: string[];
  valid_from: string;
  valid_until?: string;
  featured: boolean;
  priority: number;
  status: string;
}

interface SampleApiResponse {
  offers: SampleApiOffer[];
  total: number;
  page: number;
  per_page: number;
}

@Injectable()
export class SamplePlatformIntegration extends BasePlatformIntegration {
  constructor() {
    const configuration: PlatformConfiguration = {
      apiEndpoint: 'https://api.sample-cashback-platform.com/v1',
      apiKey: process.env.SAMPLE_PLATFORM_API_KEY,
      rateLimit: 1000,
      timeout: 30000,
      retryAttempts: 3,
    };

    super('sample-platform', configuration);
  }

  async getPlatformInfo(): Promise<CashbackPlatform> {
    const metadata: PlatformMetadata = {
      website: 'https://sample-cashback-platform.com',
      description: 'Sample cashback platform for demonstration purposes',
      supportedCountries: ['US', 'CA', 'UK'],
      supportedCurrencies: ['USD', 'CAD', 'GBP'],
      categories: ['shopping', 'travel', 'dining', 'entertainment', 'groceries'],
    };

    return new CashbackPlatform(
      this.platformId,
      'Sample Cashback Platform',
      PlatformStatus.ACTIVE,
      metadata,
      this.configuration,
    );
  }

  async fetchOffers(): Promise<CashbackOffer[]> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return this.makeApiRequest<SampleApiResponse>('/offers');
      });

      return response.offers.map(apiOffer => this.mapApiOfferToEntity(apiOffer));
    } catch (error) {
      this.logger.error('Failed to fetch offers from Sample Platform', error);
      throw new Error(`Failed to fetch offers: ${error.message}`);
    }
  }

  async fetchOffersByCategory(category: string): Promise<CashbackOffer[]> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return this.makeApiRequest<SampleApiResponse>(`/offers?category=${encodeURIComponent(category)}`);
      });

      return response.offers.map(apiOffer => this.mapApiOfferToEntity(apiOffer));
    } catch (error) {
      this.logger.error(`Failed to fetch offers by category ${category} from Sample Platform`, error);
      throw new Error(`Failed to fetch offers by category: ${error.message}`);
    }
  }

  async fetchOffersByMerchant(merchant: string): Promise<CashbackOffer[]> {
    try {
      const response = await this.retryWithBackoff(async () => {
        return this.makeApiRequest<SampleApiResponse>(`/offers?merchant=${encodeURIComponent(merchant)}`);
      });

      return response.offers.map(apiOffer => this.mapApiOfferToEntity(apiOffer));
    } catch (error) {
      this.logger.error(`Failed to fetch offers by merchant ${merchant} from Sample Platform`, error);
      throw new Error(`Failed to fetch offers by merchant: ${error.message}`);
    }
  }

  async validateConfiguration(): Promise<boolean> {
    if (!this.configuration.apiKey) {
      throw new Error('API key is required for Sample Platform integration');
    }

    if (!this.configuration.apiEndpoint) {
      throw new Error('API endpoint is required for Sample Platform integration');
    }

    try {
      // Test API connectivity
      await this.makeApiRequest<{ status: string }>('/health');
      return true;
    } catch (error) {
      throw new Error(`Configuration validation failed: ${error.message}`);
    }
  }

  async getSupportedCategories(): Promise<string[]> {
    try {
      const response = await this.makeApiRequest<{ categories: string[] }>('/categories');
      return response.categories;
    } catch (error) {
      this.logger.error('Failed to fetch supported categories from Sample Platform', error);
      // Return default categories if API call fails
      return ['shopping', 'travel', 'dining', 'entertainment', 'groceries'];
    }
  }

  async getSupportedMerchants(): Promise<string[]> {
    try {
      const response = await this.makeApiRequest<{ merchants: string[] }>('/merchants');
      return response.merchants;
    } catch (error) {
      this.logger.error('Failed to fetch supported merchants from Sample Platform', error);
      return [];
    }
  }

  protected async performHealthCheck(): Promise<void> {
    try {
      const response = await this.makeApiRequest<{ status: string; timestamp: string }>('/health');
      
      if (response.status !== 'ok') {
        throw new Error(`Platform health check failed: ${response.status}`);
      }
    } catch (error) {
      throw new Error(`Health check failed: ${error.message}`);
    }
  }

  private mapApiOfferToEntity(apiOffer: SampleApiOffer): CashbackOffer {
    const cashbackRate = new CashbackRate(
      apiOffer.cashback_percentage,
      apiOffer.max_cashback,
      apiOffer.min_spend,
    );

    const terms = {
      description: apiOffer.description,
      validFrom: new Date(apiOffer.valid_from),
      validUntil: apiOffer.valid_until ? new Date(apiOffer.valid_until) : undefined,
    };

    const metadata = {
      category: apiOffer.category,
      merchant: apiOffer.merchant,
      tags: apiOffer.tags,
      featured: apiOffer.featured,
      priority: apiOffer.priority,
    };

    const status = this.mapApiStatusToOfferStatus(apiOffer.status);

    return new CashbackOffer(
      `${this.platformId}_${apiOffer.id}`,
      this.platformId,
      apiOffer.title,
      cashbackRate,
      terms,
      metadata,
      status,
    );
  }

  private mapApiStatusToOfferStatus(apiStatus: string): OfferStatus {
    switch (apiStatus.toLowerCase()) {
      case 'active':
        return OfferStatus.ACTIVE;
      case 'expired':
        return OfferStatus.EXPIRED;
      case 'coming_soon':
        return OfferStatus.COMING_SOON;
      case 'paused':
        return OfferStatus.PAUSED;
      default:
        return OfferStatus.ACTIVE;
    }
  }
}
