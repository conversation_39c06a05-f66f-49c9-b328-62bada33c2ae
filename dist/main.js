"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const global_exception_filter_1 = require("./infrastructure/filters/global-exception.filter");
const logging_interceptor_1 = require("./infrastructure/interceptors/logging.interceptor");
const request_context_middleware_1 = require("./infrastructure/middleware/request-context.middleware");
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    app.use(new request_context_middleware_1.RequestContextMiddleware().use.bind(new request_context_middleware_1.RequestContextMiddleware()));
    app.useGlobalFilters(new global_exception_filter_1.GlobalExceptionFilter(configService));
    app.useGlobalInterceptors(new logging_interceptor_1.LoggingInterceptor());
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    const apiPrefix = configService.get('app.apiPrefix', 'api/v1');
    app.setGlobalPrefix(apiPrefix);
    app.enableCors({
        origin: true,
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true,
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Cashback Comparison Platform API')
        .setDescription('API for comparing cashback offers across multiple platforms')
        .setVersion('1.0')
        .addTag('Comparisons', 'Cashback offer comparison endpoints')
        .addTag('Analytics', 'Analytics and reporting endpoints')
        .addTag('Platforms', 'Platform management endpoints')
        .addTag('Health', 'Health check endpoints')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('docs', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
    });
    const port = configService.get('app.port', 3000);
    await app.listen(port);
    logger.log(`🚀 Application is running on: http://localhost:${port}`);
    logger.log(`📚 API Documentation: http://localhost:${port}/docs`);
    logger.log(`🔗 API Base URL: http://localhost:${port}/${apiPrefix}`);
}
bootstrap().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map