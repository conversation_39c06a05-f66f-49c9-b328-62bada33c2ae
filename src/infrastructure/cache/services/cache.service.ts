import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';

export interface CacheOptions {
  ttl?: number;
  namespace?: string;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
  ) {}

  async get<T>(key: string, namespace?: string): Promise<T | null> {
    try {
      const fullKey = this.buildKey(key, namespace);
      const value = await this.cacheManager.get<T>(fullKey);
      
      if (value) {
        this.logger.debug(`Cache hit for key: ${fullKey}`);
      } else {
        this.logger.debug(`Cache miss for key: ${fullKey}`);
      }
      
      return value || null;
    } catch (error) {
      this.logger.error(`Failed to get cache value for key: ${key}`, error);
      return null;
    }
  }

  async set<T>(
    key: string,
    value: T,
    options: CacheOptions = {},
  ): Promise<void> {
    try {
      const fullKey = this.buildKey(key, options.namespace);
      const ttl = options.ttl || this.getDefaultTtl(options.namespace);
      
      await this.cacheManager.set(fullKey, value, ttl);
      this.logger.debug(`Cache set for key: ${fullKey}, TTL: ${ttl}s`);
    } catch (error) {
      this.logger.error(`Failed to set cache value for key: ${key}`, error);
    }
  }

  async del(key: string, namespace?: string): Promise<void> {
    try {
      const fullKey = this.buildKey(key, namespace);
      await this.cacheManager.del(fullKey);
      this.logger.debug(`Cache deleted for key: ${fullKey}`);
    } catch (error) {
      this.logger.error(`Failed to delete cache value for key: ${key}`, error);
    }
  }

  async mget<T>(keys: string[], namespace?: string): Promise<(T | null)[]> {
    try {
      const fullKeys = keys.map(key => this.buildKey(key, namespace));
      const values = await Promise.all(
        fullKeys.map(key => this.cacheManager.get<T>(key))
      );
      
      return values.map(value => value || null);
    } catch (error) {
      this.logger.error(`Failed to get multiple cache values`, error);
      return keys.map(() => null);
    }
  }

  async mset<T>(
    keyValuePairs: Array<{ key: string; value: T }>,
    options: CacheOptions = {},
  ): Promise<void> {
    try {
      const ttl = options.ttl || this.getDefaultTtl(options.namespace);
      
      await Promise.all(
        keyValuePairs.map(({ key, value }) => {
          const fullKey = this.buildKey(key, options.namespace);
          return this.cacheManager.set(fullKey, value, ttl);
        })
      );
      
      this.logger.debug(`Cache set for ${keyValuePairs.length} keys, TTL: ${ttl}s`);
    } catch (error) {
      this.logger.error(`Failed to set multiple cache values`, error);
    }
  }

  async mdel(keys: string[], namespace?: string): Promise<void> {
    try {
      const fullKeys = keys.map(key => this.buildKey(key, namespace));
      await Promise.all(fullKeys.map(key => this.cacheManager.del(key)));
      this.logger.debug(`Cache deleted for ${keys.length} keys`);
    } catch (error) {
      this.logger.error(`Failed to delete multiple cache values`, error);
    }
  }

  async clear(namespace?: string): Promise<void> {
    try {
      if (namespace) {
        // For namespace-specific clearing, we'd need to implement pattern-based deletion
        // This is a simplified version
        this.logger.warn(`Namespace-specific cache clearing not fully implemented for: ${namespace}`);
      } else {
        // Use store.reset() if available, otherwise skip
        const store = (this.cacheManager as any).store;
        if (store && typeof store.reset === 'function') {
          await store.reset();
          this.logger.log('Cache cleared completely');
        } else {
          this.logger.warn('Cache reset not available in current store implementation');
        }
      }
    } catch (error) {
      this.logger.error('Failed to clear cache', error);
    }
  }

  async exists(key: string, namespace?: string): Promise<boolean> {
    try {
      const fullKey = this.buildKey(key, namespace);
      const value = await this.cacheManager.get(fullKey);
      return value !== undefined && value !== null;
    } catch (error) {
      this.logger.error(`Failed to check cache existence for key: ${key}`, error);
      return false;
    }
  }

  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    options: CacheOptions = {},
  ): Promise<T> {
    const cached = await this.get<T>(key, options.namespace);
    
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, options);
    return value;
  }

  async wrap<T>(
    key: string,
    factory: () => Promise<T>,
    options: CacheOptions = {},
  ): Promise<T> {
    return this.getOrSet(key, factory, options);
  }

  private buildKey(key: string, namespace?: string): string {
    if (namespace) {
      return `${namespace}:${key}`;
    }
    return key;
  }

  private getDefaultTtl(namespace?: string): number {
    const ttlConfig = this.configService.get('cache.ttl');
    
    switch (namespace) {
      case 'comparison':
        return ttlConfig.comparison;
      case 'offers':
        return ttlConfig.offers;
      case 'platforms':
        return ttlConfig.platforms;
      case 'analytics':
        return ttlConfig.analytics;
      case 'health':
        return ttlConfig.health;
      default:
        return ttlConfig.comparison; // Default TTL
    }
  }

  async getStats(): Promise<{
    hits: number;
    misses: number;
    keys: number;
    hitRate: number;
  }> {
    // This would require implementing stats tracking
    // For now, return placeholder values
    return {
      hits: 0,
      misses: 0,
      keys: 0,
      hitRate: 0,
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      const testKey = 'health_check';
      const testValue = Date.now().toString();

      await this.set(testKey, testValue, { ttl: 10 });
      const retrieved = await this.get(testKey);
      await this.del(testKey);

      return retrieved === testValue;
    } catch (error) {
      this.logger.error('Cache health check failed', error);
      return false;
    }
  }
}
