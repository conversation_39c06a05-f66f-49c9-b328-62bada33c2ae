# Production Environment Configuration

# Application Configuration
NODE_ENV=production
PORT=3000
API_PREFIX=api/v1

# Database Configuration
MONGODB_URI=mongodb://mongo:27017/cashback-comparison

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=cashback:

# Cache Configuration
CACHE_COMPARISON_TTL=900
CACHE_OFFERS_TTL=3600
CACHE_PLATFORMS_TTL=7200
CACHE_ANALYTICS_TTL=1800
CACHE_HEALTH_TTL=300
CACHE_MAX_COMPARISONS=10000
CACHE_MAX_OFFERS=50000
CACHE_MAX_PLATFORMS=100
CACHE_MAX_ANALYTICS=1000

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log

# External APIs (configure with actual API keys)
# SAMPLE_PLATFORM_API_KEY=your_api_key_here
# Add other platform API keys as needed
