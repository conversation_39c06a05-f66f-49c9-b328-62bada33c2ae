{"version": 3, "file": "comparison.mapper.js", "sourceRoot": "", "sources": ["../../../../src/modules/api/mappers/comparison.mapper.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAkBrC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,wBAAwB,CAAC,MAAwB;QAC/C,OAAO;YACL,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC1D,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC/E,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,OAAO,CAAC;YACvD,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACzC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;SAC1C,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,UAAe;QAC7C,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC;YACnD,YAAY,EAAE,UAAU,CAAC,YAAY;YACrC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC;YACnE,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,KAAK,EAAE,UAAU,CAAC,KAAK;SACxB,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,KAAoB;QAChD,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,YAAY,CAAC;YAC3D,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC;YAC3C,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpD,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;YACxC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;SACzC,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,KAAY;QAChC,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;SACzB,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,IAAkB;QAC7C,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,KAAU;QACnC,OAAO;YACL,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;YACxC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;YAC3C,cAAc,EAAE,KAAK,CAAC,cAAc;YACpC,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;SAC7C,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,QAAa;QACzC,OAAO;YACL,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;SAC5B,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,OAAY;QAC5C,OAAO;YACL,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;SAC3C,CAAC;IACJ,CAAC;IAEO,0BAA0B,CAAC,QAAa;QAC9C,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC;YACrD,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;SAC5B,CAAC;IACJ,CAAC;CACF,CAAA;AA7FY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;GACA,gBAAgB,CA6F5B"}