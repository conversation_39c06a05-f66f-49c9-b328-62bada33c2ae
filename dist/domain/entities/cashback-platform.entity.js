"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashbackPlatform = void 0;
const platform_status_enum_1 = require("../enums/platform-status.enum");
class CashbackPlatform {
    _id;
    _name;
    _status;
    _metadata;
    _configuration;
    _createdAt;
    _updatedAt;
    constructor(_id, _name, _status, _metadata, _configuration, _createdAt = new Date(), _updatedAt = new Date()) {
        this._id = _id;
        this._name = _name;
        this._status = _status;
        this._metadata = _metadata;
        this._configuration = _configuration;
        this._createdAt = _createdAt;
        this._updatedAt = _updatedAt;
        this.validateId();
        this.validateName();
        this.validateMetadata();
    }
    get id() {
        return this._id;
    }
    get name() {
        return this._name;
    }
    get status() {
        return this._status;
    }
    get metadata() {
        return this._metadata;
    }
    get configuration() {
        return this._configuration;
    }
    get createdAt() {
        return this._createdAt;
    }
    get updatedAt() {
        return this._updatedAt;
    }
    validateId() {
        if (!this._id || this._id.trim().length === 0) {
            throw new Error('Platform ID cannot be empty');
        }
    }
    validateName() {
        if (!this._name || this._name.trim().length === 0) {
            throw new Error('Platform name cannot be empty');
        }
    }
    validateMetadata() {
        if (!this._metadata.website) {
            throw new Error('Platform website is required');
        }
        if (!this._metadata.supportedCountries || this._metadata.supportedCountries.length === 0) {
            throw new Error('Platform must support at least one country');
        }
        if (!this._metadata.supportedCurrencies || this._metadata.supportedCurrencies.length === 0) {
            throw new Error('Platform must support at least one currency');
        }
    }
    isActive() {
        return this._status === platform_status_enum_1.PlatformStatus.ACTIVE;
    }
    supportsCountry(countryCode) {
        return this._metadata.supportedCountries.includes(countryCode);
    }
    supportsCurrency(currencyCode) {
        return this._metadata.supportedCurrencies.includes(currencyCode);
    }
    hasCategory(category) {
        return this._metadata.categories.includes(category);
    }
    updateStatus(newStatus) {
        if (this._status !== newStatus) {
            this._updatedAt = new Date();
            return new CashbackPlatform(this._id, this._name, newStatus, this._metadata, this._configuration, this._createdAt, this._updatedAt);
        }
        return this;
    }
    touch() {
        this._updatedAt = new Date();
    }
    equals(other) {
        return this._id === other._id;
    }
    toString() {
        return `${this._name} (${this._id})`;
    }
}
exports.CashbackPlatform = CashbackPlatform;
//# sourceMappingURL=cashback-platform.entity.js.map