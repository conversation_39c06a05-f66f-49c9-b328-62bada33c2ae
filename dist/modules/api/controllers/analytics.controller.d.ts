import { ComparisonAnalyticsService } from '../../comparison/services/comparison-analytics.service';
import { ComparisonAnalyticsDto, TrendDataDto, PlatformReportDto } from '../dto/analytics-response.dto';
declare class AnalyticsQueryDto {
    startDate?: string;
    endDate?: string;
}
declare class TrendQueryDto {
    days?: number;
    granularity?: 'day' | 'week' | 'month';
}
export declare class AnalyticsController {
    private readonly analyticsService;
    private readonly logger;
    constructor(analyticsService: ComparisonAnalyticsService);
    getAnalyticsOverview(query: AnalyticsQueryDto): Promise<ComparisonAnalyticsDto>;
    getTrendData(query: TrendQueryDto): Promise<TrendDataDto[]>;
    getPlatformAnalytics(platformId: string): Promise<PlatformReportDto>;
    getTopCategories(limit?: number): Promise<any[]>;
    getTopMerchants(limit?: number): Promise<any[]>;
    getPlatformPerformance(): Promise<any[]>;
}
export {};
