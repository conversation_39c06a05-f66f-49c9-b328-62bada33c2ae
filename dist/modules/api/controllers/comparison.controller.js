"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ComparisonController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const comparison_engine_service_1 = require("../../comparison/services/comparison-engine.service");
const comparison_mapper_1 = require("../mappers/comparison.mapper");
const comparison_request_dto_1 = require("../dto/comparison-request.dto");
const comparison_response_dto_1 = require("../dto/comparison-response.dto");
let ComparisonController = ComparisonController_1 = class ComparisonController {
    comparisonEngineService;
    comparisonMapper;
    logger = new common_1.Logger(ComparisonController_1.name);
    constructor(comparisonEngineService, comparisonMapper) {
        this.comparisonEngineService = comparisonEngineService;
        this.comparisonMapper = comparisonMapper;
    }
    async compareOffers(request, options) {
        try {
            this.logger.log(`Comparing offers for ${request.spendAmount} ${request.currency || 'USD'}`);
            const result = await this.comparisonEngineService.compareOffers(request, options);
            return this.comparisonMapper.mapComparisonResultToDto(result);
        }
        catch (error) {
            this.logger.error('Failed to compare offers', error);
            throw new common_1.HttpException(`Comparison failed: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async syncAndCompare(request, options) {
        try {
            this.logger.log(`Syncing and comparing offers for ${request.spendAmount} ${request.currency || 'USD'}`);
            const result = await this.comparisonEngineService.syncAndCompare(request, options);
            return this.comparisonMapper.mapComparisonResultToDto(result);
        }
        catch (error) {
            this.logger.error('Failed to sync and compare offers', error);
            throw new common_1.HttpException(`Sync and comparison failed: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getComparison(id) {
        try {
            throw new common_1.HttpException('Get comparison by ID not yet implemented', common_1.HttpStatus.NOT_IMPLEMENTED);
        }
        catch (error) {
            this.logger.error(`Failed to get comparison ${id}`, error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`Failed to retrieve comparison: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async refreshComparison(id) {
        try {
            this.logger.log(`Refreshing comparison ${id}`);
            const result = await this.comparisonEngineService.refreshComparison(id);
            return this.comparisonMapper.mapComparisonResultToDto(result);
        }
        catch (error) {
            this.logger.error(`Failed to refresh comparison ${id}`, error);
            if (error.message.includes('not found')) {
                throw new common_1.HttpException(`Comparison result not found: ${id}`, common_1.HttpStatus.NOT_FOUND);
            }
            throw new common_1.HttpException(`Failed to refresh comparison: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getComparisonHistory(limit, category, merchant) {
        try {
            const criteria = {
                ...(category && { category }),
                ...(merchant && { merchant }),
            };
            const results = await this.comparisonEngineService.getComparisonHistory(criteria, limit || 10);
            return results.map(result => this.comparisonMapper.mapComparisonResultToDto(result));
        }
        catch (error) {
            this.logger.error('Failed to get comparison history', error);
            throw new common_1.HttpException(`Failed to retrieve comparison history: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ComparisonController = ComparisonController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Compare cashback offers',
        description: 'Compare cashback offers across multiple platforms based on specified criteria',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Comparison completed successfully',
        type: comparison_response_dto_1.ComparisonResultDto,
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Invalid request parameters',
    }),
    (0, swagger_1.ApiInternalServerErrorResponse)({
        description: 'Internal server error during comparison',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [comparison_request_dto_1.ComparisonRequestDto,
        comparison_request_dto_1.ComparisonOptionsDto]),
    __metadata("design:returntype", Promise)
], ComparisonController.prototype, "compareOffers", null);
__decorate([
    (0, common_1.Post)('sync-and-compare'),
    (0, swagger_1.ApiOperation)({
        summary: 'Sync platform data and compare offers',
        description: 'Sync latest data from platforms and then perform comparison',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Sync and comparison completed successfully',
        type: comparison_response_dto_1.ComparisonResultDto,
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Invalid request parameters',
    }),
    (0, swagger_1.ApiInternalServerErrorResponse)({
        description: 'Internal server error during sync or comparison',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [comparison_request_dto_1.ComparisonRequestDto,
        comparison_request_dto_1.ComparisonOptionsDto]),
    __metadata("design:returntype", Promise)
], ComparisonController.prototype, "syncAndCompare", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get comparison result by ID',
        description: 'Retrieve a previously computed comparison result',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Comparison result ID',
        example: 'comp_1640995200000_abc123',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Comparison result retrieved successfully',
        type: comparison_response_dto_1.ComparisonResultDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Comparison result not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ComparisonController.prototype, "getComparison", null);
__decorate([
    (0, common_1.Post)(':id/refresh'),
    (0, swagger_1.ApiOperation)({
        summary: 'Refresh comparison result',
        description: 'Refresh an existing comparison with latest data',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Comparison result ID to refresh',
        example: 'comp_1640995200000_abc123',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Comparison refreshed successfully',
        type: comparison_response_dto_1.ComparisonResultDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Comparison result not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ComparisonController.prototype, "refreshComparison", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get comparison history',
        description: 'Retrieve recent comparison results',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Maximum number of results to return',
        required: false,
        example: 10,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'category',
        description: 'Filter by category',
        required: false,
        example: 'shopping',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'merchant',
        description: 'Filter by merchant',
        required: false,
        example: 'Amazon',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Comparison history retrieved successfully',
        type: [comparison_response_dto_1.ComparisonResultDto],
    }),
    __param(0, (0, common_1.Query)('limit')),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Query)('merchant')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], ComparisonController.prototype, "getComparisonHistory", null);
exports.ComparisonController = ComparisonController = ComparisonController_1 = __decorate([
    (0, swagger_1.ApiTags)('Comparisons'),
    (0, common_1.Controller)('comparisons'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __metadata("design:paramtypes", [comparison_engine_service_1.ComparisonEngineService,
        comparison_mapper_1.ComparisonMapper])
], ComparisonController);
//# sourceMappingURL=comparison.controller.js.map