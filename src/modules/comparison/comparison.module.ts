import { Module } from '@nestjs/common';
import { ComparisonEngineService } from './services/comparison-engine.service';
import { ComparisonAnalyticsService } from './services/comparison-analytics.service';
import { ComparisonService } from '../../domain/services/comparison.service';
import { PlatformIntegrationModule } from '../../infrastructure/integrations/platform-integration.module';
import { RepositoryModule } from '../../infrastructure/repositories/repository.module';

@Module({
  imports: [
    PlatformIntegrationModule,
    RepositoryModule,
  ],
  providers: [
    ComparisonService,
    ComparisonEngineService,
    ComparisonAnalyticsService,
  ],
  exports: [
    ComparisonEngineService,
    ComparisonAnalyticsService,
    ComparisonService,
  ],
})
export class ComparisonModule {}
