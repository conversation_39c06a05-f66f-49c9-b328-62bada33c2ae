{"version": 3, "file": "comparison-result.entity.js", "sourceRoot": "", "sources": ["../../../src/domain/entities/comparison-result.entity.ts"], "names": [], "mappings": ";;;AAAA,wDAAkD;AAElD,wEAAiE;AA0BjE,MAAa,gBAAgB;IAER;IACA;IACA;IACA;IACA;IACA;IACT;IAPV,YACmB,GAAW,EACX,SAA6B,EAC7B,YAA+B,EAC/B,QAA2B,EAC3B,UAA4B,uCAAgB,CAAC,SAAS,EACtD,aAAmB,IAAI,IAAI,EAAE,EACtC,aAAmB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAN/C,QAAG,GAAH,GAAG,CAAQ;QACX,cAAS,GAAT,SAAS,CAAoB;QAC7B,iBAAY,GAAZ,YAAY,CAAmB;QAC/B,aAAQ,GAAR,QAAQ,CAAmB;QAC3B,YAAO,GAAP,OAAO,CAA+C;QACtD,eAAU,GAAV,UAAU,CAAmB;QACtC,eAAU,GAAV,UAAU,CAA8C;QAEhE,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAI,WAAW;QACb,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAE9B,MAAM,YAAY,GAAG,CAAC,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC7E,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,YAAY,CAAC;YACtB,CAAC;YAGD,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;YAC9C,UAAU,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACpE,CAAC;IAED,YAAY,CAAC,QAAgB,CAAC;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,mBAAmB,CAAC,YAAoB;QACtC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,CAAC;IACxE,CAAC;IAED,mBAAmB,CAAC,QAAgB;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAClC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,CACnE,CAAC;IACJ,CAAC;IAED,wBAAwB;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,gBAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,SAAS,CAAC,iBAAiB,CAAC;IACrC,CAAC;IAED,SAAS;QACP,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;IACtC,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,OAAO,KAAK,uCAAgB,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1E,CAAC;IAED,YAAY,CAAC,iBAAyB;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACxF,CAAC;IAED,MAAM,CAAC,KAAuB;QAC5B,OAAO,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC;IAChC,CAAC;IAED,QAAQ;QACN,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,MAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;QACtF,OAAO,kBAAkB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,mBAAmB,YAAY,EAAE,CAAC;IAClG,CAAC;CACF;AAlID,4CAkIC"}