import { PlatformIntegration, SyncResult } from '../interfaces/platform-integration.interface';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform } from '../../../domain/entities/cashback-platform.entity';
export interface PlatformSyncOptions {
    platformIds?: string[];
    forceFullSync?: boolean;
    maxConcurrency?: number;
    skipHealthCheck?: boolean;
}
export interface BatchSyncResult {
    totalPlatforms: number;
    successfulSyncs: number;
    failedSyncs: number;
    results: Map<string, SyncResult>;
    totalDuration: number;
}
export declare class PlatformIntegrationStrategy {
    private readonly logger;
    private readonly integrations;
    registerIntegration(integration: PlatformIntegration): void;
    unregisterIntegration(platformId: string): void;
    getIntegration(platformId: string): PlatformIntegration | undefined;
    getAllIntegrations(): PlatformIntegration[];
    getRegisteredPlatformIds(): string[];
    syncAllPlatforms(options?: PlatformSyncOptions): Promise<BatchSyncResult>;
    syncSinglePlatform(platformId: string, options?: PlatformSyncOptions): Promise<SyncResult>;
    fetchOffersFromAllPlatforms(): Promise<Map<string, CashbackOffer[]>>;
    fetchOffersByCategory(category: string): Promise<Map<string, CashbackOffer[]>>;
    fetchOffersByMerchant(merchant: string): Promise<Map<string, CashbackOffer[]>>;
    checkAllPlatformsHealth(): Promise<Map<string, boolean>>;
    getPlatformInfo(platformId: string): Promise<CashbackPlatform>;
    getAllPlatformInfo(): Promise<Map<string, CashbackPlatform>>;
}
