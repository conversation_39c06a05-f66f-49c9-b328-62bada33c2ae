"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const comparison_analytics_service_1 = require("../../comparison/services/comparison-analytics.service");
const analytics_response_dto_1 = require("../dto/analytics-response.dto");
class AnalyticsQueryDto {
    startDate;
    endDate;
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "endDate", void 0);
class TrendQueryDto {
    days;
    granularity;
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value, 10)),
    __metadata("design:type", Number)
], TrendQueryDto.prototype, "days", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['day', 'week', 'month']),
    __metadata("design:type", String)
], TrendQueryDto.prototype, "granularity", void 0);
let AnalyticsController = AnalyticsController_1 = class AnalyticsController {
    analyticsService;
    logger = new common_1.Logger(AnalyticsController_1.name);
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    async getAnalyticsOverview(query) {
        try {
            this.logger.log('Generating analytics overview');
            const startDate = query.startDate ? new Date(query.startDate) : undefined;
            const endDate = query.endDate ? new Date(query.endDate) : undefined;
            const analytics = await this.analyticsService.getComparisonAnalytics(startDate, endDate);
            return analytics;
        }
        catch (error) {
            this.logger.error('Failed to generate analytics overview', error);
            throw new common_1.HttpException(`Failed to generate analytics: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTrendData(query) {
        try {
            this.logger.log(`Generating trend data for ${query.days || 30} days`);
            const trendData = await this.analyticsService.getTrendData(query.days || 30, query.granularity || 'day');
            return trendData;
        }
        catch (error) {
            this.logger.error('Failed to generate trend data', error);
            throw new common_1.HttpException(`Failed to generate trend data: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPlatformAnalytics(platformId) {
        try {
            this.logger.log(`Generating analytics for platform ${platformId}`);
            const report = await this.analyticsService.getPlatformComparisonReport(platformId);
            if (report.totalComparisons === 0) {
                throw new common_1.HttpException(`No data found for platform: ${platformId}`, common_1.HttpStatus.NOT_FOUND);
            }
            return report;
        }
        catch (error) {
            this.logger.error(`Failed to generate platform analytics for ${platformId}`, error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(`Failed to generate platform analytics: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTopCategories(limit) {
        try {
            this.logger.log('Getting top categories');
            const analytics = await this.analyticsService.getComparisonAnalytics();
            return analytics.topCategories.slice(0, limit || 10);
        }
        catch (error) {
            this.logger.error('Failed to get top categories', error);
            throw new common_1.HttpException(`Failed to get top categories: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getTopMerchants(limit) {
        try {
            this.logger.log('Getting top merchants');
            const analytics = await this.analyticsService.getComparisonAnalytics();
            return analytics.topMerchants.slice(0, limit || 10);
        }
        catch (error) {
            this.logger.error('Failed to get top merchants', error);
            throw new common_1.HttpException(`Failed to get top merchants: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPlatformPerformance() {
        try {
            this.logger.log('Getting platform performance data');
            const analytics = await this.analyticsService.getComparisonAnalytics();
            return analytics.platformPerformance;
        }
        catch (error) {
            this.logger.error('Failed to get platform performance data', error);
            throw new common_1.HttpException(`Failed to get platform performance data: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('overview'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get comparison analytics overview',
        description: 'Retrieve comprehensive analytics about comparison performance and trends',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Start date for analytics (ISO 8601)',
        required: false,
        example: '2024-01-01T00:00:00Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'End date for analytics (ISO 8601)',
        required: false,
        example: '2024-12-31T23:59:59Z',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Analytics overview retrieved successfully',
        type: analytics_response_dto_1.ComparisonAnalyticsDto,
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Invalid query parameters',
    }),
    (0, swagger_1.ApiInternalServerErrorResponse)({
        description: 'Internal server error while generating analytics',
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [AnalyticsQueryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAnalyticsOverview", null);
__decorate([
    (0, common_1.Get)('trends'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get trend data',
        description: 'Retrieve trend data for comparisons over time',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'days',
        description: 'Number of days to include in trend data',
        required: false,
        example: 30,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'granularity',
        description: 'Granularity of trend data',
        required: false,
        example: 'day',
        enum: ['day', 'week', 'month'],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Trend data retrieved successfully',
        type: [analytics_response_dto_1.TrendDataDto],
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Invalid query parameters',
    }),
    (0, swagger_1.ApiInternalServerErrorResponse)({
        description: 'Internal server error while generating trend data',
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TrendQueryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getTrendData", null);
__decorate([
    (0, common_1.Get)('platforms/:platformId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get platform-specific analytics',
        description: 'Retrieve detailed analytics for a specific platform',
    }),
    (0, swagger_1.ApiParam)({
        name: 'platformId',
        description: 'Platform ID to analyze',
        example: 'sample-platform',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Platform analytics retrieved successfully',
        type: analytics_response_dto_1.PlatformReportDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Platform not found',
    }),
    (0, swagger_1.ApiInternalServerErrorResponse)({
        description: 'Internal server error while generating platform analytics',
    }),
    __param(0, (0, common_1.Param)('platformId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getPlatformAnalytics", null);
__decorate([
    (0, common_1.Get)('categories/top'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get top categories',
        description: 'Retrieve the most popular categories by comparison count',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Maximum number of categories to return',
        required: false,
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Top categories retrieved successfully',
    }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getTopCategories", null);
__decorate([
    (0, common_1.Get)('merchants/top'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get top merchants',
        description: 'Retrieve the most popular merchants by comparison count',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Maximum number of merchants to return',
        required: false,
        example: 10,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Top merchants retrieved successfully',
    }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getTopMerchants", null);
__decorate([
    (0, common_1.Get)('platforms/performance'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get platform performance comparison',
        description: 'Compare performance metrics across all platforms',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Platform performance data retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getPlatformPerformance", null);
exports.AnalyticsController = AnalyticsController = AnalyticsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Analytics'),
    (0, common_1.Controller)('analytics'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __metadata("design:paramtypes", [comparison_analytics_service_1.ComparisonAnalyticsService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map