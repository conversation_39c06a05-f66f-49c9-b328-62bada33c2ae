import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CategoryStatsDto {
  @ApiProperty({
    description: 'Category name',
    example: 'shopping',
  })
  category: string;

  @ApiProperty({
    description: 'Number of comparisons in this category',
    example: 150,
  })
  comparisonCount: number;

  @ApiProperty({
    description: 'Average cashback rate for this category',
    example: 4.2,
  })
  averageCashbackRate: number;

  @ApiProperty({
    description: 'Total offers in this category',
    example: 450,
  })
  totalOffers: number;
}

export class MerchantStatsDto {
  @ApiProperty({
    description: 'Merchant name',
    example: 'Amazon',
  })
  merchant: string;

  @ApiProperty({
    description: 'Number of comparisons for this merchant',
    example: 75,
  })
  comparisonCount: number;

  @ApiProperty({
    description: 'Average cashback rate for this merchant',
    example: 3.8,
  })
  averageCashbackRate: number;

  @ApiProperty({
    description: 'Total offers for this merchant',
    example: 125,
  })
  totalOffers: number;
}

export class PlatformStatsDto {
  @ApiProperty({
    description: 'Platform ID',
    example: 'platform1',
  })
  platformId: string;

  @ApiProperty({
    description: 'Platform name',
    example: 'Sample Cashback Platform',
  })
  platformName: string;

  @ApiProperty({
    description: 'Total offers from this platform',
    example: 200,
  })
  totalOffers: number;

  @ApiProperty({
    description: 'Average cashback rate for this platform',
    example: 4.5,
  })
  averageCashbackRate: number;

  @ApiProperty({
    description: 'Win rate percentage (how often this platform has the best offer)',
    example: 35.2,
  })
  winRate: number;

  @ApiProperty({
    description: 'Average rank in comparisons',
    example: 2.3,
  })
  averageRank: number;
}

export class ComparisonAnalyticsDto {
  @ApiProperty({
    description: 'Total number of comparisons',
    example: 1250,
  })
  totalComparisons: number;

  @ApiProperty({
    description: 'Average number of offers per comparison',
    example: 8.5,
  })
  averageOffersPerComparison: number;

  @ApiProperty({
    description: 'Top categories by comparison count',
    type: [CategoryStatsDto],
  })
  topCategories: CategoryStatsDto[];

  @ApiProperty({
    description: 'Top merchants by comparison count',
    type: [MerchantStatsDto],
  })
  topMerchants: MerchantStatsDto[];

  @ApiProperty({
    description: 'Platform performance statistics',
    type: [PlatformStatsDto],
  })
  platformPerformance: PlatformStatsDto[];

  @ApiProperty({
    description: 'Average processing time in milliseconds',
    example: 185,
  })
  averageProcessingTime: number;

  @ApiProperty({
    description: 'Cache hit rate percentage',
    example: 65.5,
  })
  cacheHitRate: number;
}

export class TrendDataDto {
  @ApiProperty({
    description: 'Date or period',
    example: '2024-01-01',
  })
  date: string;

  @ApiProperty({
    description: 'Number of comparisons in this period',
    example: 45,
  })
  comparisons: number;

  @ApiProperty({
    description: 'Average cashback rate in this period',
    example: 4.1,
  })
  averageCashbackRate: number;

  @ApiProperty({
    description: 'Top category in this period',
    example: 'shopping',
  })
  topCategory: string;
}

export class PlatformReportDto {
  @ApiProperty({
    description: 'Total comparisons involving this platform',
    example: 320,
  })
  totalComparisons: number;

  @ApiProperty({
    description: 'Win rate percentage',
    example: 28.5,
  })
  winRate: number;

  @ApiProperty({
    description: 'Average rank in comparisons',
    example: 2.8,
  })
  averageRank: number;

  @ApiProperty({
    description: 'Average cashback rate',
    example: 4.2,
  })
  averageCashbackRate: number;

  @ApiProperty({
    description: 'Top categories for this platform',
    example: ['shopping', 'travel', 'dining'],
  })
  topCategories: string[];

  @ApiProperty({
    description: 'Recent performance trend',
    type: [TrendDataDto],
  })
  recentPerformance: TrendDataDto[];
}
