import { Injectable, Logger } from '@nestjs/common';
import { PlatformIntegration, SyncResult } from '../interfaces/platform-integration.interface';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform } from '../../../domain/entities/cashback-platform.entity';

export interface PlatformSyncOptions {
  platformIds?: string[];
  forceFullSync?: boolean;
  maxConcurrency?: number;
  skipHealthCheck?: boolean;
}

export interface BatchSyncResult {
  totalPlatforms: number;
  successfulSyncs: number;
  failedSyncs: number;
  results: Map<string, SyncResult>;
  totalDuration: number;
}

@Injectable()
export class PlatformIntegrationStrategy {
  private readonly logger = new Logger(PlatformIntegrationStrategy.name);
  private readonly integrations = new Map<string, PlatformIntegration>();

  registerIntegration(integration: PlatformIntegration): void {
    const platformId = integration.getPlatformId();
    this.integrations.set(platformId, integration);
    this.logger.log(`Registered integration for platform: ${platformId}`);
  }

  unregisterIntegration(platformId: string): void {
    if (this.integrations.delete(platformId)) {
      this.logger.log(`Unregistered integration for platform: ${platformId}`);
    }
  }

  getIntegration(platformId: string): PlatformIntegration | undefined {
    return this.integrations.get(platformId);
  }

  getAllIntegrations(): PlatformIntegration[] {
    return Array.from(this.integrations.values());
  }

  getRegisteredPlatformIds(): string[] {
    return Array.from(this.integrations.keys());
  }

  async syncAllPlatforms(options: PlatformSyncOptions = {}): Promise<BatchSyncResult> {
    const startTime = Date.now();
    const platformIds = options.platformIds || this.getRegisteredPlatformIds();
    const maxConcurrency = options.maxConcurrency || 3;
    
    this.logger.log(`Starting batch sync for ${platformIds.length} platforms with concurrency ${maxConcurrency}`);

    const results = new Map<string, SyncResult>();
    let successfulSyncs = 0;
    let failedSyncs = 0;

    // Process platforms in batches to respect concurrency limits
    for (let i = 0; i < platformIds.length; i += maxConcurrency) {
      const batch = platformIds.slice(i, i + maxConcurrency);
      const batchPromises = batch.map(platformId => this.syncSinglePlatform(platformId, options));
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        const platformId = batch[index];
        
        if (result.status === 'fulfilled') {
          results.set(platformId, result.value);
          if (result.value.success) {
            successfulSyncs++;
          } else {
            failedSyncs++;
          }
        } else {
          failedSyncs++;
          results.set(platformId, {
            success: false,
            offersProcessed: 0,
            offersAdded: 0,
            offersUpdated: 0,
            offersRemoved: 0,
            errors: [result.reason?.message || 'Unknown error'],
            syncDuration: 0,
          });
        }
      });
    }

    const totalDuration = Date.now() - startTime;
    
    this.logger.log(`Batch sync completed: ${successfulSyncs} successful, ${failedSyncs} failed, ${totalDuration}ms total`);

    return {
      totalPlatforms: platformIds.length,
      successfulSyncs,
      failedSyncs,
      results,
      totalDuration,
    };
  }

  async syncSinglePlatform(platformId: string, options: PlatformSyncOptions = {}): Promise<SyncResult> {
    const integration = this.getIntegration(platformId);
    
    if (!integration) {
      throw new Error(`No integration found for platform: ${platformId}`);
    }

    // Health check before sync (unless skipped)
    if (!options.skipHealthCheck) {
      const health = await integration.checkHealth();
      if (!health.isHealthy) {
        throw new Error(`Platform ${platformId} is not healthy: ${health.errorMessage}`);
      }
    }

    // Determine sync type
    if (options.forceFullSync) {
      return integration.syncOffers();
    } else {
      // Try incremental sync if we have a last sync time
      // For now, we'll use full sync as default
      return integration.syncOffers();
    }
  }

  async fetchOffersFromAllPlatforms(): Promise<Map<string, CashbackOffer[]>> {
    const results = new Map<string, CashbackOffer[]>();
    const integrations = this.getAllIntegrations();

    const promises = integrations.map(async (integration) => {
      try {
        const offers = await integration.fetchOffers();
        results.set(integration.getPlatformId(), offers);
      } catch (error) {
        this.logger.error(`Failed to fetch offers from platform ${integration.getPlatformId()}`, error);
        results.set(integration.getPlatformId(), []);
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  async fetchOffersByCategory(category: string): Promise<Map<string, CashbackOffer[]>> {
    const results = new Map<string, CashbackOffer[]>();
    const integrations = this.getAllIntegrations();

    const promises = integrations.map(async (integration) => {
      try {
        const offers = await integration.fetchOffersByCategory(category);
        results.set(integration.getPlatformId(), offers);
      } catch (error) {
        this.logger.error(`Failed to fetch offers by category from platform ${integration.getPlatformId()}`, error);
        results.set(integration.getPlatformId(), []);
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  async fetchOffersByMerchant(merchant: string): Promise<Map<string, CashbackOffer[]>> {
    const results = new Map<string, CashbackOffer[]>();
    const integrations = this.getAllIntegrations();

    const promises = integrations.map(async (integration) => {
      try {
        const offers = await integration.fetchOffersByMerchant(merchant);
        results.set(integration.getPlatformId(), offers);
      } catch (error) {
        this.logger.error(`Failed to fetch offers by merchant from platform ${integration.getPlatformId()}`, error);
        results.set(integration.getPlatformId(), []);
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  async checkAllPlatformsHealth(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const integrations = this.getAllIntegrations();

    const promises = integrations.map(async (integration) => {
      try {
        const health = await integration.checkHealth();
        results.set(integration.getPlatformId(), health.isHealthy);
      } catch (error) {
        this.logger.error(`Health check failed for platform ${integration.getPlatformId()}`, error);
        results.set(integration.getPlatformId(), false);
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  async getPlatformInfo(platformId: string): Promise<CashbackPlatform> {
    const integration = this.getIntegration(platformId);
    
    if (!integration) {
      throw new Error(`No integration found for platform: ${platformId}`);
    }

    return integration.getPlatformInfo();
  }

  async getAllPlatformInfo(): Promise<Map<string, CashbackPlatform>> {
    const results = new Map<string, CashbackPlatform>();
    const integrations = this.getAllIntegrations();

    const promises = integrations.map(async (integration) => {
      try {
        const platformInfo = await integration.getPlatformInfo();
        results.set(integration.getPlatformId(), platformInfo);
      } catch (error) {
        this.logger.error(`Failed to get platform info for ${integration.getPlatformId()}`, error);
      }
    });

    await Promise.allSettled(promises);
    return results;
  }
}
