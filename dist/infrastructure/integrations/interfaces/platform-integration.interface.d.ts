import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform } from '../../../domain/entities/cashback-platform.entity';
export interface PlatformHealthStatus {
    isHealthy: boolean;
    responseTime: number;
    lastChecked: Date;
    errorMessage?: string;
}
export interface SyncResult {
    success: boolean;
    offersProcessed: number;
    offersAdded: number;
    offersUpdated: number;
    offersRemoved: number;
    errors: string[];
    syncDuration: number;
}
export interface RateLimitInfo {
    remaining: number;
    resetTime: Date;
    limit: number;
}
export interface PlatformIntegration {
    getPlatformId(): string;
    getPlatformInfo(): Promise<CashbackPlatform>;
    checkHealth(): Promise<PlatformHealthStatus>;
    fetchOffers(): Promise<CashbackOffer[]>;
    fetchOffersByCategory(category: string): Promise<CashbackOffer[]>;
    fetchOffersByMerchant(merchant: string): Promise<CashbackOffer[]>;
    syncOffers(): Promise<SyncResult>;
    syncOffersIncremental(lastSyncTime: Date): Promise<SyncResult>;
    getRateLimitInfo(): Promise<RateLimitInfo>;
    validateConfiguration(): Promise<boolean>;
    getSupportedCategories(): Promise<string[]>;
    getSupportedMerchants(): Promise<string[]>;
}
