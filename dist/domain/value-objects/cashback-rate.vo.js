"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashbackRate = void 0;
class CashbackRate {
    _percentage;
    _maxAmount;
    _minSpend;
    constructor(_percentage, _maxAmount, _minSpend) {
        this._percentage = _percentage;
        this._maxAmount = _maxAmount;
        this._minSpend = _minSpend;
        this.validatePercentage();
        this.validateMaxAmount();
        this.validateMinSpend();
    }
    get percentage() {
        return this._percentage;
    }
    get maxAmount() {
        return this._maxAmount;
    }
    get minSpend() {
        return this._minSpend;
    }
    validatePercentage() {
        if (this._percentage < 0 || this._percentage > 100) {
            throw new Error('Cashback percentage must be between 0 and 100');
        }
    }
    validateMaxAmount() {
        if (this._maxAmount !== undefined && this._maxAmount < 0) {
            throw new Error('Max amount must be positive');
        }
    }
    validateMinSpend() {
        if (this._minSpend !== undefined && this._minSpend < 0) {
            throw new Error('Min spend must be positive');
        }
    }
    calculateCashback(spendAmount) {
        if (this._minSpend && spendAmount < this._minSpend) {
            return 0;
        }
        const cashback = (spendAmount * this._percentage) / 100;
        if (this._maxAmount && cashback > this._maxAmount) {
            return this._maxAmount;
        }
        return cashback;
    }
    equals(other) {
        return (this._percentage === other._percentage &&
            this._maxAmount === other._maxAmount &&
            this._minSpend === other._minSpend);
    }
    toString() {
        let result = `${this._percentage}%`;
        if (this._maxAmount) {
            result += ` (max $${this._maxAmount})`;
        }
        if (this._minSpend) {
            result += ` (min spend $${this._minSpend})`;
        }
        return result;
    }
}
exports.CashbackRate = CashbackRate;
//# sourceMappingURL=cashback-rate.vo.js.map