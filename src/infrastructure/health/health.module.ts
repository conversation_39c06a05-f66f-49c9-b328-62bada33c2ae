import { Modu<PERSON> } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './health.controller';
import { DatabaseHealthIndicator } from './indicators/database-health.indicator';
import { CacheHealthIndicator } from './indicators/cache-health.indicator';
import { PlatformHealthIndicator } from './indicators/platform-health.indicator';
import { PlatformIntegrationModule } from '../integrations/platform-integration.module';

@Module({
  imports: [
    TerminusModule,
    HttpModule,
    PlatformIntegrationModule,
  ],
  controllers: [HealthController],
  providers: [
    DatabaseHealthIndicator,
    CacheHealthIndicator,
    PlatformHealthIndicator,
  ],
})
export class HealthModule {}
