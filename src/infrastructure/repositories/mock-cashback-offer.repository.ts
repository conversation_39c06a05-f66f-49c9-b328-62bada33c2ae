import { Injectable, Logger } from '@nestjs/common';
import { CashbackOffer } from '../../domain/entities/cashback-offer.entity';
import { CashbackOfferRepository, FindOffersOptions } from '../../domain/repositories/cashback-offer.repository';

@Injectable()
export class MockCashbackOfferRepository implements CashbackOfferRepository {
  private readonly logger = new Logger(MockCashbackOfferRepository.name);
  private offers: CashbackOffer[] = [];

  async findById(id: string): Promise<CashbackOffer | null> {
    return this.offers.find(offer => offer.id === id) || null;
  }

  async findByPlatform(platformId: string, options?: FindOffersOptions): Promise<CashbackOffer[]> {
    let filtered = this.offers.filter(offer => offer.platformId === platformId);
    
    if (options?.status) {
      filtered = filtered.filter(offer => offer.status === options.status);
    }
    
    if (options?.category) {
      filtered = filtered.filter(offer => offer.belongsToCategory(options.category!));
    }

    if (options?.merchant) {
      filtered = filtered.filter(offer => offer.belongsToMerchant(options.merchant!));
    }
    
    if (options?.limit) {
      filtered = filtered.slice(0, options.limit);
    }
    
    return filtered;
  }

  async findAll(options?: FindOffersOptions): Promise<CashbackOffer[]> {
    let filtered = [...this.offers];
    
    if (options?.platformId) {
      filtered = filtered.filter(offer => offer.platformId === options.platformId);
    }
    
    if (options?.status) {
      filtered = filtered.filter(offer => offer.status === options.status);
    }
    
    if (options?.category) {
      filtered = filtered.filter(offer => offer.belongsToCategory(options.category!));
    }

    if (options?.merchant) {
      filtered = filtered.filter(offer => offer.belongsToMerchant(options.merchant!));
    }
    
    if (options?.featured !== undefined) {
      filtered = filtered.filter(offer => offer.metadata.featured === options.featured);
    }
    
    if (options?.validAt) {
      filtered = filtered.filter(offer => offer.isValid(options.validAt));
    }
    
    if (options?.limit) {
      filtered = filtered.slice(0, options.limit);
    }
    
    return filtered;
  }

  async findActive(options?: Omit<FindOffersOptions, 'status'>): Promise<CashbackOffer[]> {
    return this.findAll({ ...options, status: 'active' as any });
  }

  async findByCategory(category: string, options?: FindOffersOptions): Promise<CashbackOffer[]> {
    return this.findAll({ ...options, category });
  }

  async findByMerchant(merchant: string, options?: FindOffersOptions): Promise<CashbackOffer[]> {
    return this.findAll({ ...options, merchant });
  }

  async findFeatured(options?: FindOffersOptions): Promise<CashbackOffer[]> {
    return this.findAll({ ...options, featured: true });
  }

  async save(offer: CashbackOffer): Promise<CashbackOffer> {
    const existingIndex = this.offers.findIndex(o => o.id === offer.id);
    
    if (existingIndex >= 0) {
      this.offers[existingIndex] = offer;
    } else {
      this.offers.push(offer);
    }
    
    this.logger.debug(`Saved offer: ${offer.id}`);
    return offer;
  }

  async saveMany(offers: CashbackOffer[]): Promise<CashbackOffer[]> {
    for (const offer of offers) {
      await this.save(offer);
    }
    return offers;
  }

  async update(id: string, offer: Partial<CashbackOffer>): Promise<CashbackOffer> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error(`Offer not found: ${id}`);
    }
    
    // In a real implementation, this would properly update the entity
    const updated = { ...existing, ...offer } as CashbackOffer;
    return this.save(updated);
  }

  async delete(id: string): Promise<void> {
    const index = this.offers.findIndex(offer => offer.id === id);
    if (index >= 0) {
      this.offers.splice(index, 1);
      this.logger.debug(`Deleted offer: ${id}`);
    }
  }

  async deleteByPlatform(platformId: string): Promise<void> {
    this.offers = this.offers.filter(offer => offer.platformId !== platformId);
    this.logger.debug(`Deleted offers for platform: ${platformId}`);
  }

  async count(options?: FindOffersOptions): Promise<number> {
    const filtered = await this.findAll(options);
    return filtered.length;
  }

  async exists(id: string): Promise<boolean> {
    return this.offers.some(offer => offer.id === id);
  }
}
