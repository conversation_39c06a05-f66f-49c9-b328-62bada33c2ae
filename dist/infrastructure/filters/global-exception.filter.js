"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GlobalExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let GlobalExceptionFilter = GlobalExceptionFilter_1 = class GlobalExceptionFilter {
    configService;
    logger = new common_1.Logger(GlobalExceptionFilter_1.name);
    constructor(configService) {
        this.configService = configService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const { status, message, error, details } = this.getErrorInfo(exception);
        const requestId = this.generateRequestId();
        const errorResponse = {
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method,
            message,
            requestId,
        };
        if (this.configService.get('app.nodeEnv') === 'development') {
            errorResponse.error = error;
            if (details) {
                errorResponse.details = details;
            }
        }
        this.logError(exception, request, errorResponse);
        response.status(status).json(errorResponse);
    }
    getErrorInfo(exception) {
        if (exception instanceof common_1.HttpException) {
            const response = exception.getResponse();
            const status = exception.getStatus();
            if (typeof response === 'object' && response !== null) {
                return {
                    status,
                    message: response.message || exception.message,
                    error: response.error,
                    details: response.details,
                };
            }
            return {
                status,
                message: exception.message,
                error: exception.name,
            };
        }
        if (exception instanceof Error) {
            if (exception.name === 'ValidationError') {
                return {
                    status: common_1.HttpStatus.BAD_REQUEST,
                    message: 'Validation failed',
                    error: exception.name,
                    details: exception.message,
                };
            }
            if (exception.name === 'MongoError' || exception.name === 'MongooseError') {
                return {
                    status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Database operation failed',
                    error: 'DatabaseError',
                    details: this.configService.get('app.nodeEnv') === 'development'
                        ? exception.message
                        : undefined,
                };
            }
            if (exception.name === 'TimeoutError') {
                return {
                    status: common_1.HttpStatus.REQUEST_TIMEOUT,
                    message: 'Request timeout',
                    error: exception.name,
                };
            }
            if (exception.name === 'UnauthorizedError') {
                return {
                    status: common_1.HttpStatus.UNAUTHORIZED,
                    message: 'Unauthorized access',
                    error: exception.name,
                };
            }
            return {
                status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Internal server error',
                error: exception.name,
                details: this.configService.get('app.nodeEnv') === 'development'
                    ? exception.message
                    : undefined,
            };
        }
        return {
            status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
            message: 'An unexpected error occurred',
            error: 'UnknownError',
            details: this.configService.get('app.nodeEnv') === 'development'
                ? String(exception)
                : undefined,
        };
    }
    logError(exception, request, errorResponse) {
        const { statusCode, requestId, path, method } = errorResponse;
        const logContext = {
            requestId,
            method,
            path,
            statusCode,
            userAgent: request.get('User-Agent'),
            ip: request.ip,
            query: request.query,
            body: this.sanitizeBody(request.body),
        };
        if (statusCode >= 500) {
            this.logger.error(`${method} ${path} - ${statusCode}`, exception instanceof Error ? exception.stack : String(exception), JSON.stringify(logContext));
        }
        else if (statusCode >= 400) {
            this.logger.warn(`${method} ${path} - ${statusCode}: ${errorResponse.message}`, JSON.stringify(logContext));
        }
        else {
            this.logger.log(`${method} ${path} - ${statusCode}`, JSON.stringify(logContext));
        }
    }
    sanitizeBody(body) {
        if (!body || typeof body !== 'object') {
            return body;
        }
        const sensitiveFields = ['password', 'token', 'apiKey', 'secret', 'authorization'];
        const sanitized = { ...body };
        for (const field of sensitiveFields) {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        }
        return sanitized;
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = GlobalExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map