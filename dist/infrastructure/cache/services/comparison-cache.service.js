"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ComparisonCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonCacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("./cache.service");
let ComparisonCacheService = ComparisonCacheService_1 = class ComparisonCacheService {
    cacheService;
    logger = new common_1.Logger(ComparisonCacheService_1.name);
    namespace = 'comparison';
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    async getComparison(criteria) {
        const key = this.buildCriteriaKey(criteria);
        return this.cacheService.get(key, this.namespace);
    }
    async setComparison(result) {
        const key = this.buildCriteriaKey(result.criteria);
        const ttl = this.calculateTtl(result);
        await this.cacheService.set(key, result, {
            namespace: this.namespace,
            ttl,
        });
    }
    async getComparisonById(id) {
        return this.cacheService.get(id, this.namespace);
    }
    async setComparisonById(result) {
        const ttl = this.calculateTtl(result);
        await this.cacheService.set(result.id, result, {
            namespace: this.namespace,
            ttl,
        });
    }
    async invalidateComparison(criteria) {
        const key = this.buildCriteriaKey(criteria);
        await this.cacheService.del(key, this.namespace);
    }
    async invalidateComparisonById(id) {
        await this.cacheService.del(id, this.namespace);
    }
    async getRecentComparisons(limit = 10) {
        return [];
    }
    async cacheComparisonHistory(results, key = 'recent') {
        await this.cacheService.set(key, results, {
            namespace: `${this.namespace}:history`,
            ttl: 3600,
        });
    }
    async getComparisonHistory(key = 'recent') {
        const cached = await this.cacheService.get(key, `${this.namespace}:history`);
        return cached || [];
    }
    async invalidateExpiredComparisons() {
        this.logger.log('Invalidating expired comparisons (not fully implemented)');
    }
    async getComparisonStats() {
        return {
            totalCached: 0,
            hitRate: 0,
            averageAge: 0,
        };
    }
    buildCriteriaKey(criteria) {
        const keyParts = [
            `amount:${criteria.spendAmount.amount}`,
            `currency:${criteria.spendAmount.currency}`,
        ];
        if (criteria.category) {
            keyParts.push(`category:${criteria.category}`);
        }
        if (criteria.merchant) {
            keyParts.push(`merchant:${criteria.merchant}`);
        }
        if (criteria.country) {
            keyParts.push(`country:${criteria.country}`);
        }
        return keyParts.join('|');
    }
    calculateTtl(result) {
        const baseTime = 900;
        const timeUntilExpiry = result.expiresAt.getTime() - Date.now();
        return Math.min(baseTime, Math.floor(timeUntilExpiry / 1000));
    }
    async warmupCache(commonCriteria, resultFactory) {
        this.logger.log(`Warming up cache with ${commonCriteria.length} common criteria`);
        const promises = commonCriteria.map(async (criteria) => {
            try {
                const existing = await this.getComparison(criteria);
                if (!existing || existing.isExpired()) {
                    const result = await resultFactory(criteria);
                    await this.setComparison(result);
                }
            }
            catch (error) {
                this.logger.warn(`Failed to warm up cache for criteria`, error);
            }
        });
        await Promise.allSettled(promises);
        this.logger.log('Cache warmup completed');
    }
    async preloadPopularComparisons() {
        this.logger.log('Preloading popular comparisons (not fully implemented)');
    }
};
exports.ComparisonCacheService = ComparisonCacheService;
exports.ComparisonCacheService = ComparisonCacheService = ComparisonCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], ComparisonCacheService);
//# sourceMappingURL=comparison-cache.service.js.map