import { Logger } from '@nestjs/common';
import { PlatformIntegration, PlatformHealthStatus, SyncResult, RateLimitInfo } from '../interfaces/platform-integration.interface';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform, PlatformConfiguration } from '../../../domain/entities/cashback-platform.entity';

export abstract class BasePlatformIntegration implements PlatformIntegration {
  protected readonly logger = new Logger(this.constructor.name);
  protected lastSyncTime?: Date;
  protected rateLimitInfo?: RateLimitInfo;

  constructor(
    protected readonly platformId: string,
    protected readonly configuration: PlatformConfiguration,
  ) {}

  abstract getPlatformInfo(): Promise<CashbackPlatform>;
  abstract fetchOffers(): Promise<CashbackOffer[]>;
  abstract validateConfiguration(): Promise<boolean>;

  getPlatformId(): string {
    return this.platformId;
  }

  async checkHealth(): Promise<PlatformHealthStatus> {
    const startTime = Date.now();
    
    try {
      await this.performHealthCheck();
      const responseTime = Date.now() - startTime;
      
      return {
        isHealthy: true,
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Health check failed for platform ${this.platformId}`, error);
      
      return {
        isHealthy: false,
        responseTime,
        lastChecked: new Date(),
        errorMessage: error.message,
      };
    }
  }

  async fetchOffersByCategory(category: string): Promise<CashbackOffer[]> {
    try {
      const allOffers = await this.fetchOffers();
      return allOffers.filter(offer => offer.belongsToCategory(category));
    } catch (error) {
      this.logger.error(`Failed to fetch offers by category ${category} for platform ${this.platformId}`, error);
      throw error;
    }
  }

  async fetchOffersByMerchant(merchant: string): Promise<CashbackOffer[]> {
    try {
      const allOffers = await this.fetchOffers();
      return allOffers.filter(offer => offer.belongsToMerchant(merchant));
    } catch (error) {
      this.logger.error(`Failed to fetch offers by merchant ${merchant} for platform ${this.platformId}`, error);
      throw error;
    }
  }

  async syncOffers(): Promise<SyncResult> {
    const startTime = Date.now();
    const result: SyncResult = {
      success: false,
      offersProcessed: 0,
      offersAdded: 0,
      offersUpdated: 0,
      offersRemoved: 0,
      errors: [],
      syncDuration: 0,
    };

    try {
      this.logger.log(`Starting full sync for platform ${this.platformId}`);
      
      const offers = await this.fetchOffers();
      result.offersProcessed = offers.length;
      
      // This would typically involve comparing with existing offers
      // and determining what needs to be added, updated, or removed
      const syncResults = await this.processSyncResults(offers);
      
      result.offersAdded = syncResults.added;
      result.offersUpdated = syncResults.updated;
      result.offersRemoved = syncResults.removed;
      result.success = true;
      
      this.lastSyncTime = new Date();
      this.logger.log(`Sync completed for platform ${this.platformId}: ${result.offersProcessed} offers processed`);
      
    } catch (error) {
      this.logger.error(`Sync failed for platform ${this.platformId}`, error);
      result.errors.push(error.message);
    } finally {
      result.syncDuration = Date.now() - startTime;
    }

    return result;
  }

  async syncOffersIncremental(lastSyncTime: Date): Promise<SyncResult> {
    // Default implementation falls back to full sync
    // Subclasses can override this for true incremental sync
    this.logger.warn(`Incremental sync not implemented for platform ${this.platformId}, falling back to full sync`);
    return this.syncOffers();
  }

  async getRateLimitInfo(): Promise<RateLimitInfo> {
    if (this.rateLimitInfo) {
      return this.rateLimitInfo;
    }

    // Default rate limit info if not implemented by subclass
    return {
      remaining: 1000,
      resetTime: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
      limit: 1000,
    };
  }

  async getSupportedCategories(): Promise<string[]> {
    // Default implementation - subclasses should override
    return [];
  }

  async getSupportedMerchants(): Promise<string[]> {
    // Default implementation - subclasses should override
    return [];
  }

  protected async performHealthCheck(): Promise<void> {
    // Default health check - can be overridden by subclasses
    await this.validateConfiguration();
  }

  protected async processSyncResults(offers: CashbackOffer[]): Promise<{
    added: number;
    updated: number;
    removed: number;
  }> {
    // This is a placeholder implementation
    // In a real implementation, this would compare with existing offers
    // and determine what needs to be added, updated, or removed
    return {
      added: offers.length,
      updated: 0,
      removed: 0,
    };
  }

  protected async makeApiRequest<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.configuration.apiEndpoint}${endpoint}`;
    const timeout = this.configuration.timeout || 30000;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.configuration.apiKey}`,
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      // Update rate limit info if available
      this.updateRateLimitInfo(response);

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${timeout}ms`);
      }
      
      throw error;
    }
  }

  protected updateRateLimitInfo(response: Response): void {
    const remaining = response.headers.get('X-RateLimit-Remaining');
    const reset = response.headers.get('X-RateLimit-Reset');
    const limit = response.headers.get('X-RateLimit-Limit');

    if (remaining && reset && limit) {
      this.rateLimitInfo = {
        remaining: parseInt(remaining, 10),
        resetTime: new Date(parseInt(reset, 10) * 1000),
        limit: parseInt(limit, 10),
      };
    }
  }

  protected async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error = new Error('Unknown error');

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          break;
        }

        const delay = baseDelay * Math.pow(2, attempt);
        this.logger.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms`, lastError.message);
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
