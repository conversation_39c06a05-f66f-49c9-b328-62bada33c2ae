"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashbackOffer = void 0;
const money_vo_1 = require("../value-objects/money.vo");
const platform_status_enum_1 = require("../enums/platform-status.enum");
class CashbackOffer {
    _id;
    _platformId;
    _title;
    _cashbackRate;
    _terms;
    _metadata;
    _status;
    _createdAt;
    _updatedAt;
    constructor(_id, _platformId, _title, _cashbackRate, _terms, _metadata, _status = platform_status_enum_1.OfferStatus.ACTIVE, _createdAt = new Date(), _updatedAt = new Date()) {
        this._id = _id;
        this._platformId = _platformId;
        this._title = _title;
        this._cashbackRate = _cashbackRate;
        this._terms = _terms;
        this._metadata = _metadata;
        this._status = _status;
        this._createdAt = _createdAt;
        this._updatedAt = _updatedAt;
        this.validateId();
        this.validatePlatformId();
        this.validateTitle();
        this.validateTerms();
        this.validateMetadata();
    }
    get id() {
        return this._id;
    }
    get platformId() {
        return this._platformId;
    }
    get title() {
        return this._title;
    }
    get cashbackRate() {
        return this._cashbackRate;
    }
    get terms() {
        return this._terms;
    }
    get metadata() {
        return this._metadata;
    }
    get status() {
        return this._status;
    }
    get createdAt() {
        return this._createdAt;
    }
    get updatedAt() {
        return this._updatedAt;
    }
    validateId() {
        if (!this._id || this._id.trim().length === 0) {
            throw new Error('Offer ID cannot be empty');
        }
    }
    validatePlatformId() {
        if (!this._platformId || this._platformId.trim().length === 0) {
            throw new Error('Platform ID cannot be empty');
        }
    }
    validateTitle() {
        if (!this._title || this._title.trim().length === 0) {
            throw new Error('Offer title cannot be empty');
        }
    }
    validateTerms() {
        if (!this._terms.description) {
            throw new Error('Offer description is required');
        }
        if (this._terms.validUntil && this._terms.validUntil <= this._terms.validFrom) {
            throw new Error('Valid until date must be after valid from date');
        }
    }
    validateMetadata() {
        if (!this._metadata.category) {
            throw new Error('Offer category is required');
        }
        if (!this._metadata.merchant) {
            throw new Error('Offer merchant is required');
        }
    }
    isActive() {
        return this._status === platform_status_enum_1.OfferStatus.ACTIVE;
    }
    isValid(date = new Date()) {
        if (!this.isActive()) {
            return false;
        }
        if (date < this._terms.validFrom) {
            return false;
        }
        if (this._terms.validUntil && date > this._terms.validUntil) {
            return false;
        }
        if (this._terms.maxRedemptions &&
            this._terms.currentRedemptions &&
            this._terms.currentRedemptions >= this._terms.maxRedemptions) {
            return false;
        }
        return true;
    }
    calculateCashback(spendAmount) {
        if (!this.isValid()) {
            return new money_vo_1.Money(0, spendAmount.currency);
        }
        const cashbackAmount = this._cashbackRate.calculateCashback(spendAmount.amount);
        return new money_vo_1.Money(cashbackAmount, spendAmount.currency);
    }
    belongsToCategory(category) {
        return this._metadata.category.toLowerCase() === category.toLowerCase();
    }
    belongsToMerchant(merchant) {
        return this._metadata.merchant.toLowerCase() === merchant.toLowerCase();
    }
    hasTag(tag) {
        return this._metadata.tags.some(t => t.toLowerCase() === tag.toLowerCase());
    }
    touch() {
        this._updatedAt = new Date();
    }
    equals(other) {
        return this._id === other._id && this._platformId === other._platformId;
    }
    toString() {
        return `${this._title} - ${this._cashbackRate.toString()} (${this._metadata.merchant})`;
    }
}
exports.CashbackOffer = CashbackOffer;
//# sourceMappingURL=cashback-offer.entity.js.map