"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const cache_service_1 = require("../../cache/services/cache.service");
let CacheHealthIndicator = class CacheHealthIndicator extends terminus_1.HealthIndicator {
    cacheService;
    constructor(cacheService) {
        super();
        this.cacheService = cacheService;
    }
    async isHealthy(key) {
        try {
            const startTime = Date.now();
            const isHealthy = await this.cacheService.healthCheck();
            if (!isHealthy) {
                throw new Error('Cache health check failed');
            }
            const responseTime = Date.now() - startTime;
            const result = this.getStatus(key, true, {
                state: 'connected',
                responseTime: `${responseTime}ms`,
                type: 'redis',
            });
            return result;
        }
        catch (error) {
            const result = this.getStatus(key, false, {
                state: 'disconnected',
                error: error.message,
                type: 'redis',
            });
            throw new terminus_1.HealthCheckError('Cache health check failed', result);
        }
    }
};
exports.CacheHealthIndicator = CacheHealthIndicator;
exports.CacheHealthIndicator = CacheHealthIndicator = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], CacheHealthIndicator);
//# sourceMappingURL=cache-health.indicator.js.map