# Database Configuration
MONGODB_URI=mongodb://localhost:27017/cashback-comparison
MONGODB_TEST_URI=mongodb://localhost:27017/cashback-comparison-test

# Application Configuration
PORT=3000
NODE_ENV=development

# API Configuration
API_PREFIX=api/v1

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log

# External APIs (to be configured later)
# PLATFORM_API_KEYS will be added here for different cashback platforms
