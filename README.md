# Cashback Comparison Platform

A scalable backend service for comparing cashback offers across multiple platforms, built with NestJS, TypeScript, and MongoDB.

## Features

- **Multi-Platform Integration**: Extensible architecture for integrating with various cashback platforms
- **Intelligent Comparison Engine**: Advanced scoring algorithm for ranking cashback offers
- **Caching Strategy**: Redis-based caching for optimal performance
- **Comprehensive Analytics**: Detailed analytics and reporting capabilities
- **Health Monitoring**: Built-in health checks and monitoring
- **Production Ready**: Docker containerization, logging, and error handling

## Architecture

The application follows clean architecture principles with clear separation of concerns:

- **Domain Layer**: Core business logic, entities, and value objects
- **Infrastructure Layer**: External integrations, caching, and data persistence
- **Application Layer**: Use cases and application services
- **API Layer**: REST endpoints with validation and documentation

## Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- MongoDB (if running locally)
- Redis (if running locally)

### Development Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd cashback-comparison-platform
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment**

   ```bash
   cp .env.development .env
   ```

4. **Start services with Docker**

   ```bash
   make docker-up-dev
   ```

5. **Start the application**

   ```bash
   npm run start:dev
   ```

6. **Access the application**
   - API: http://localhost:3000
   - API Documentation: http://localhost:3000/docs
   - Health Check: http://localhost:3000/health

### Using Make Commands

The project includes a Makefile for common operations:

```bash
# Development
make setup-dev          # Set up development environment
make start-dev          # Start in development mode
make test               # Run tests
make lint               # Run linter

# Docker
make docker-up-dev      # Start development services
make docker-down-dev    # Stop development services
make docker-logs        # View logs

# Production
make docker-up          # Start production services
make deploy-prod        # Deploy to production
```

## API Documentation

The API is documented using Swagger/OpenAPI. Once the application is running, visit:

- http://localhost:3000/docs

### Key Endpoints

- `POST /api/v1/comparisons` - Compare cashback offers
- `GET /api/v1/comparisons/{id}` - Get comparison result
- `GET /api/v1/analytics/overview` - Get analytics overview
- `GET /api/v1/health` - Health check

## Configuration

### Environment Variables

| Variable      | Description                          | Default                                       |
| ------------- | ------------------------------------ | --------------------------------------------- |
| `NODE_ENV`    | Environment (development/production) | development                                   |
| `PORT`        | Application port                     | 3000                                          |
| `MONGODB_URI` | MongoDB connection string            | mongodb://localhost:27017/cashback-comparison |
| `REDIS_HOST`  | Redis host                           | localhost                                     |
| `REDIS_PORT`  | Redis port                           | 6379                                          |

See `.env.example` for a complete list of configuration options.

## Development

### Project Structure

```
src/
├── config/              # Configuration files
├── domain/              # Domain layer (entities, value objects, services)
├── infrastructure/      # Infrastructure layer (integrations, cache, logging)
├── modules/            # Application modules
│   ├── api/            # API controllers and DTOs
│   └── comparison/     # Comparison business logic
└── database/           # Database configuration
```

### Adding New Platform Integrations

1. Create a new integration class extending `BasePlatformIntegration`
2. Implement required methods for fetching offers
3. Register the integration in `PlatformIntegrationModule`

Example:

```typescript
@Injectable()
export class NewPlatformIntegration extends BasePlatformIntegration {
  async fetchOffers(): Promise<CashbackOffer[]> {
    // Implementation
  }
}
```

### Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Deployment

### Docker Production Deployment

1. **Build and start services**

   ```bash
   make deploy-prod
   ```

2. **Monitor services**
   ```bash
   make monitor
   ```

### Environment-Specific Deployments

- **Development**: `docker-compose -f docker-compose.dev.yml up`
- **Production**: `docker-compose up`

## Monitoring and Logging

### Health Checks

- Application: `GET /health`
- Database: `GET /health/database`
- Cache: `GET /health/cache`
- Platforms: `GET /health/platforms`

### Logging

Logs are written to:

- Console (development)
- Files in `logs/` directory
- Structured JSON format for production

### Metrics

The application provides metrics through:

- Health check endpoints
- Analytics API
- Application logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
