import { CashbackPlatform } from '../entities/cashback-platform.entity';
import { PlatformStatus } from '../enums/platform-status.enum';

export interface FindPlatformsOptions {
  status?: PlatformStatus;
  country?: string;
  currency?: string;
  category?: string;
  limit?: number;
  offset?: number;
}

export interface CashbackPlatformRepository {
  findById(id: string): Promise<CashbackPlatform | null>;
  findByName(name: string): Promise<CashbackPlatform | null>;
  findAll(options?: FindPlatformsOptions): Promise<CashbackPlatform[]>;
  findActive(): Promise<CashbackPlatform[]>;
  save(platform: CashbackPlatform): Promise<CashbackPlatform>;
  update(id: string, platform: Partial<CashbackPlatform>): Promise<CashbackPlatform>;
  delete(id: string): Promise<void>;
  count(options?: FindPlatformsOptions): Promise<number>;
  exists(id: string): Promise<boolean>;
}
