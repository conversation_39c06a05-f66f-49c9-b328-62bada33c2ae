import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Injectable()
export class DatabaseHealthIndicator extends HealthIndicator {
  constructor(@InjectConnection() private connection: Connection) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const startTime = Date.now();
      
      // Check connection state
      if (this.connection.readyState !== 1) {
        throw new Error(`Database connection state: ${this.connection.readyState}`);
      }

      // Perform a simple ping operation
      if (this.connection.db) {
        await this.connection.db.admin().ping();
      }
      
      const responseTime = Date.now() - startTime;
      
      const result = this.getStatus(key, true, {
        state: 'connected',
        responseTime: `${responseTime}ms`,
        database: this.connection.name,
        host: this.connection.host,
        port: this.connection.port,
      });

      return result;
    } catch (error) {
      const result = this.getStatus(key, false, {
        state: 'disconnected',
        error: error.message,
        database: this.connection.name || 'unknown',
      });

      throw new HealthCheckError('Database health check failed', result);
    }
  }
}
