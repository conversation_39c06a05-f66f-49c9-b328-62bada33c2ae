"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OfferCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfferCacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("./cache.service");
let OfferCacheService = OfferCacheService_1 = class OfferCacheService {
    cacheService;
    logger = new common_1.Logger(OfferCacheService_1.name);
    namespace = 'offers';
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    async getOffer(id) {
        return this.cacheService.get(id, this.namespace);
    }
    async setOffer(offer) {
        const ttl = this.calculateOfferTtl(offer);
        await this.cacheService.set(offer.id, offer, {
            namespace: this.namespace,
            ttl,
        });
    }
    async getOffers(ids) {
        return this.cacheService.mget(ids, this.namespace);
    }
    async setOffers(offers) {
        const keyValuePairs = offers.map(offer => ({
            key: offer.id,
            value: offer,
        }));
        await this.cacheService.mset(keyValuePairs, {
            namespace: this.namespace,
        });
    }
    async getOffersByPlatform(platformId) {
        const key = `platform:${platformId}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setOffersByPlatform(platformId, offers) {
        const key = `platform:${platformId}`;
        await this.cacheService.set(key, offers, {
            namespace: this.namespace,
            ttl: 3600,
        });
    }
    async getOffersByCategory(category) {
        const key = `category:${category.toLowerCase()}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setOffersByCategory(category, offers) {
        const key = `category:${category.toLowerCase()}`;
        await this.cacheService.set(key, offers, {
            namespace: this.namespace,
            ttl: 1800,
        });
    }
    async getOffersByMerchant(merchant) {
        const key = `merchant:${merchant.toLowerCase()}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setOffersByMerchant(merchant, offers) {
        const key = `merchant:${merchant.toLowerCase()}`;
        await this.cacheService.set(key, offers, {
            namespace: this.namespace,
            ttl: 1800,
        });
    }
    async getFeaturedOffers() {
        const key = 'featured';
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setFeaturedOffers(offers) {
        const key = 'featured';
        await this.cacheService.set(key, offers, {
            namespace: this.namespace,
            ttl: 1800,
        });
    }
    async invalidateOffer(id) {
        await this.cacheService.del(id, this.namespace);
    }
    async invalidateOffers(ids) {
        await this.cacheService.mdel(ids, this.namespace);
    }
    async invalidatePlatformOffers(platformId) {
        const key = `platform:${platformId}`;
        await this.cacheService.del(key, this.namespace);
    }
    async invalidateCategoryOffers(category) {
        const key = `category:${category.toLowerCase()}`;
        await this.cacheService.del(key, this.namespace);
    }
    async invalidateMerchantOffers(merchant) {
        const key = `merchant:${merchant.toLowerCase()}`;
        await this.cacheService.del(key, this.namespace);
    }
    async invalidateExpiredOffers() {
        this.logger.log('Invalidating expired offers (not fully implemented)');
    }
    async refreshOfferCache(platformId, offerFactory) {
        try {
            this.logger.log(`Refreshing offer cache for platform: ${platformId}`);
            const offers = await offerFactory();
            await this.setOffers(offers);
            await this.setOffersByPlatform(platformId, offers);
            const categorizedOffers = this.groupOffersByCategory(offers);
            for (const [category, categoryOffers] of categorizedOffers) {
                await this.setOffersByCategory(category, categoryOffers);
            }
            const merchantOffers = this.groupOffersByMerchant(offers);
            for (const [merchant, merchantOfferList] of merchantOffers) {
                await this.setOffersByMerchant(merchant, merchantOfferList);
            }
            const featuredOffers = offers.filter(offer => offer.metadata.featured);
            if (featuredOffers.length > 0) {
                await this.setFeaturedOffers(featuredOffers);
            }
            this.logger.log(`Cached ${offers.length} offers for platform: ${platformId}`);
        }
        catch (error) {
            this.logger.error(`Failed to refresh offer cache for platform: ${platformId}`, error);
        }
    }
    calculateOfferTtl(offer) {
        const baseTime = 3600;
        if (offer.terms.validUntil) {
            const timeUntilExpiry = offer.terms.validUntil.getTime() - Date.now();
            const hoursUntilExpiry = timeUntilExpiry / (1000 * 60 * 60);
            if (hoursUntilExpiry < 1) {
                return 300;
            }
            else if (hoursUntilExpiry < 24) {
                return 1800;
            }
        }
        return baseTime;
    }
    groupOffersByCategory(offers) {
        const grouped = new Map();
        offers.forEach(offer => {
            const category = offer.metadata.category.toLowerCase();
            const existing = grouped.get(category) || [];
            existing.push(offer);
            grouped.set(category, existing);
        });
        return grouped;
    }
    groupOffersByMerchant(offers) {
        const grouped = new Map();
        offers.forEach(offer => {
            const merchant = offer.metadata.merchant.toLowerCase();
            const existing = grouped.get(merchant) || [];
            existing.push(offer);
            grouped.set(merchant, existing);
        });
        return grouped;
    }
    async getOfferStats() {
        return {
            totalCached: 0,
            byPlatform: {},
            byCategory: {},
            expiringSoon: 0,
        };
    }
};
exports.OfferCacheService = OfferCacheService;
exports.OfferCacheService = OfferCacheService = OfferCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], OfferCacheService);
//# sourceMappingURL=offer-cache.service.js.map