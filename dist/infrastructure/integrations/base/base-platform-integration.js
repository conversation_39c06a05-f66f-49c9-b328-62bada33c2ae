"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasePlatformIntegration = void 0;
const common_1 = require("@nestjs/common");
class BasePlatformIntegration {
    platformId;
    configuration;
    logger = new common_1.Logger(this.constructor.name);
    lastSyncTime;
    rateLimitInfo;
    constructor(platformId, configuration) {
        this.platformId = platformId;
        this.configuration = configuration;
    }
    getPlatformId() {
        return this.platformId;
    }
    async checkHealth() {
        const startTime = Date.now();
        try {
            await this.performHealthCheck();
            const responseTime = Date.now() - startTime;
            return {
                isHealthy: true,
                responseTime,
                lastChecked: new Date(),
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            this.logger.error(`Health check failed for platform ${this.platformId}`, error);
            return {
                isHealthy: false,
                responseTime,
                lastChecked: new Date(),
                errorMessage: error.message,
            };
        }
    }
    async fetchOffersByCategory(category) {
        try {
            const allOffers = await this.fetchOffers();
            return allOffers.filter(offer => offer.belongsToCategory(category));
        }
        catch (error) {
            this.logger.error(`Failed to fetch offers by category ${category} for platform ${this.platformId}`, error);
            throw error;
        }
    }
    async fetchOffersByMerchant(merchant) {
        try {
            const allOffers = await this.fetchOffers();
            return allOffers.filter(offer => offer.belongsToMerchant(merchant));
        }
        catch (error) {
            this.logger.error(`Failed to fetch offers by merchant ${merchant} for platform ${this.platformId}`, error);
            throw error;
        }
    }
    async syncOffers() {
        const startTime = Date.now();
        const result = {
            success: false,
            offersProcessed: 0,
            offersAdded: 0,
            offersUpdated: 0,
            offersRemoved: 0,
            errors: [],
            syncDuration: 0,
        };
        try {
            this.logger.log(`Starting full sync for platform ${this.platformId}`);
            const offers = await this.fetchOffers();
            result.offersProcessed = offers.length;
            const syncResults = await this.processSyncResults(offers);
            result.offersAdded = syncResults.added;
            result.offersUpdated = syncResults.updated;
            result.offersRemoved = syncResults.removed;
            result.success = true;
            this.lastSyncTime = new Date();
            this.logger.log(`Sync completed for platform ${this.platformId}: ${result.offersProcessed} offers processed`);
        }
        catch (error) {
            this.logger.error(`Sync failed for platform ${this.platformId}`, error);
            result.errors.push(error.message);
        }
        finally {
            result.syncDuration = Date.now() - startTime;
        }
        return result;
    }
    async syncOffersIncremental(lastSyncTime) {
        this.logger.warn(`Incremental sync not implemented for platform ${this.platformId}, falling back to full sync`);
        return this.syncOffers();
    }
    async getRateLimitInfo() {
        if (this.rateLimitInfo) {
            return this.rateLimitInfo;
        }
        return {
            remaining: 1000,
            resetTime: new Date(Date.now() + 60 * 60 * 1000),
            limit: 1000,
        };
    }
    async getSupportedCategories() {
        return [];
    }
    async getSupportedMerchants() {
        return [];
    }
    async performHealthCheck() {
        await this.validateConfiguration();
    }
    async processSyncResults(offers) {
        return {
            added: offers.length,
            updated: 0,
            removed: 0,
        };
    }
    async makeApiRequest(endpoint, options = {}) {
        const url = `${this.configuration.apiEndpoint}${endpoint}`;
        const timeout = this.configuration.timeout || 30000;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.configuration.apiKey}`,
                    ...options.headers,
                },
            });
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }
            this.updateRateLimitInfo(response);
            return await response.json();
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout}ms`);
            }
            throw error;
        }
    }
    updateRateLimitInfo(response) {
        const remaining = response.headers.get('X-RateLimit-Remaining');
        const reset = response.headers.get('X-RateLimit-Reset');
        const limit = response.headers.get('X-RateLimit-Limit');
        if (remaining && reset && limit) {
            this.rateLimitInfo = {
                remaining: parseInt(remaining, 10),
                resetTime: new Date(parseInt(reset, 10) * 1000),
                limit: parseInt(limit, 10),
            };
        }
    }
    async retryWithBackoff(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError = new Error('Unknown error');
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt === maxRetries) {
                    break;
                }
                const delay = baseDelay * Math.pow(2, attempt);
                this.logger.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms`, lastError.message);
                await this.sleep(delay);
            }
        }
        throw lastError;
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.BasePlatformIntegration = BasePlatformIntegration;
//# sourceMappingURL=base-platform-integration.js.map