import { ComparisonEngineService } from '../../comparison/services/comparison-engine.service';
import { ComparisonMapper } from '../mappers/comparison.mapper';
import { ComparisonRequestDto, ComparisonOptionsDto } from '../dto/comparison-request.dto';
import { ComparisonResultDto } from '../dto/comparison-response.dto';
export declare class ComparisonController {
    private readonly comparisonEngineService;
    private readonly comparisonMapper;
    private readonly logger;
    constructor(comparisonEngineService: ComparisonEngineService, comparisonMapper: ComparisonMapper);
    compareOffers(request: ComparisonRequestDto, options: ComparisonOptionsDto): Promise<ComparisonResultDto>;
    syncAndCompare(request: ComparisonRequestDto, options: ComparisonOptionsDto): Promise<ComparisonResultDto>;
    getComparison(id: string): Promise<ComparisonResultDto>;
    refreshComparison(id: string): Promise<ComparisonResultDto>;
    getComparisonHistory(limit?: number, category?: string, merchant?: string): Promise<ComparisonResultDto[]>;
}
