# Deployment Guide - Cashback Comparison Platform

## 🚀 Production Deployment

### Prerequisites

- Docker and Docker Compose
- Domain name (optional)
- SSL certificate (recommended)
- MongoDB Atlas or self-hosted MongoDB
- Redis Cloud or self-hosted Redis

### Environment Setup

1. **Create production environment file**
```bash
cp .env.development .env.production
```

2. **Update production variables**
```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://your-production-mongodb:27017/cashback-comparison
REDIS_HOST=your-production-redis
REDIS_PORT=6379
LOG_LEVEL=info
```

### Docker Production Deployment

1. **Build production image**
```bash
make ci-build
```

2. **Deploy with Docker Compose**
```bash
make deploy-prod
```

3. **Verify deployment**
```bash
make monitor
curl https://your-domain.com/api/v1/health
```

## 🔧 Manual Deployment

### 1. Server Setup

```bash
# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2

# Clone repository
git clone <repository-url>
cd cashback-comparison-platform
```

### 2. Application Setup

```bash
# Install dependencies
npm ci --only=production

# Build application
npm run build

# Set up environment
cp .env.production .env
```

### 3. Database Setup

**Option A: MongoDB Atlas (Recommended)**
```bash
# Update MONGODB_URI in .env with Atlas connection string
MONGODB_URI=mongodb+srv://username:<EMAIL>/cashback-comparison
```

**Option B: Self-hosted MongoDB**
```bash
# Install MongoDB
sudo apt-get install -y mongodb

# Start MongoDB service
sudo systemctl start mongodb
sudo systemctl enable mongodb
```

### 4. Redis Setup

**Option A: Redis Cloud (Recommended)**
```bash
# Update Redis configuration in .env
REDIS_HOST=your-redis-cloud-host
REDIS_PORT=your-redis-cloud-port
REDIS_PASSWORD=your-redis-password
```

**Option B: Self-hosted Redis**
```bash
# Install Redis
sudo apt-get install -y redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 5. Start Application

```bash
# Start with PM2
pm2 start dist/main.js --name "cashback-api"

# Save PM2 configuration
pm2 save
pm2 startup
```

## 🌐 Nginx Configuration

### 1. Install Nginx

```bash
sudo apt-get install -y nginx
```

### 2. Configure Nginx

Create `/etc/nginx/sites-available/cashback-api`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Health check endpoint
    location /api/v1/health {
        proxy_pass http://localhost:3000/api/v1/health;
        access_log off;
    }
}
```

### 3. Enable Site

```bash
sudo ln -s /etc/nginx/sites-available/cashback-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔒 SSL Configuration (Let's Encrypt)

```bash
# Install Certbot
sudo apt-get install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring Setup

### 1. Application Monitoring

```bash
# Monitor with PM2
pm2 monit

# View logs
pm2 logs cashback-api

# Restart application
pm2 restart cashback-api
```

### 2. Health Checks

Create `/etc/systemd/system/cashback-healthcheck.service`:

```ini
[Unit]
Description=Cashback API Health Check
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/curl -f http://localhost:3000/api/v1/health
User=www-data

[Install]
WantedBy=multi-user.target
```

Create `/etc/systemd/system/cashback-healthcheck.timer`:

```ini
[Unit]
Description=Run Cashback Health Check every 5 minutes
Requires=cashback-healthcheck.service

[Timer]
OnCalendar=*:0/5
Persistent=true

[Install]
WantedBy=timers.target
```

Enable health checks:
```bash
sudo systemctl enable cashback-healthcheck.timer
sudo systemctl start cashback-healthcheck.timer
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Build application
      run: npm run build
      
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /path/to/app
          git pull origin main
          npm ci --only=production
          npm run build
          pm2 restart cashback-api
```

## 🐳 Kubernetes Deployment

### 1. Create Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cashback-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cashback-api
  template:
    metadata:
      labels:
        app: cashback-api
    spec:
      containers:
      - name: cashback-api
        image: cashback-comparison-platform:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: cashback-secrets
              key: mongodb-uri
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: cashback-secrets
              key: redis-host
```

### 2. Create Service

```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: cashback-api-service
spec:
  selector:
    app: cashback-api
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

### 3. Deploy

```bash
kubectl apply -f k8s/
```

## 🔍 Troubleshooting

### Common Issues

1. **Application won't start**
   - Check environment variables
   - Verify database connectivity
   - Check logs: `pm2 logs cashback-api`

2. **Database connection issues**
   - Verify MongoDB URI
   - Check network connectivity
   - Ensure database exists

3. **Redis connection issues**
   - Verify Redis host and port
   - Check Redis service status
   - Test connection: `redis-cli ping`

4. **High memory usage**
   - Monitor with `pm2 monit`
   - Check for memory leaks
   - Restart application: `pm2 restart cashback-api`

### Performance Optimization

1. **Enable compression**
   ```nginx
   gzip on;
   gzip_types text/plain application/json application/javascript text/css;
   ```

2. **Cache static assets**
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

3. **Rate limiting**
   ```nginx
   limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
   limit_req zone=api burst=20 nodelay;
   ```

## 📈 Scaling

### Horizontal Scaling

1. **Load Balancer Setup**
   - Use Nginx upstream
   - Configure health checks
   - Implement session affinity if needed

2. **Database Scaling**
   - MongoDB replica sets
   - Read replicas for analytics
   - Sharding for large datasets

3. **Cache Scaling**
   - Redis Cluster
   - Redis Sentinel for high availability
   - Separate cache instances per service

### Monitoring & Alerting

1. **Application Metrics**
   - Response times
   - Error rates
   - Memory usage
   - CPU usage

2. **Infrastructure Metrics**
   - Database performance
   - Cache hit rates
   - Network latency
   - Disk usage

---

**For support and questions, please refer to the main README.md or open an issue.**
