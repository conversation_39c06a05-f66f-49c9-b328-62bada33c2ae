"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ComparisonEngineService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonEngineService = void 0;
const common_1 = require("@nestjs/common");
const comparison_service_1 = require("../../../domain/services/comparison.service");
const comparison_result_entity_1 = require("../../../domain/entities/comparison-result.entity");
const platform_integration_strategy_1 = require("../../../infrastructure/integrations/strategy/platform-integration.strategy");
const money_vo_1 = require("../../../domain/value-objects/money.vo");
let ComparisonEngineService = ComparisonEngineService_1 = class ComparisonEngineService {
    comparisonService;
    offerRepository;
    platformRepository;
    comparisonResultRepository;
    platformIntegrationStrategy;
    logger = new common_1.Logger(ComparisonEngineService_1.name);
    constructor(comparisonService, offerRepository, platformRepository, comparisonResultRepository, platformIntegrationStrategy) {
        this.comparisonService = comparisonService;
        this.offerRepository = offerRepository;
        this.platformRepository = platformRepository;
        this.comparisonResultRepository = comparisonResultRepository;
        this.platformIntegrationStrategy = platformIntegrationStrategy;
    }
    async compareOffers(request, options = {}) {
        this.logger.log(`Starting comparison for ${request.spendAmount} ${request.currency || 'USD'}`);
        const criteria = {
            spendAmount: new money_vo_1.Money(request.spendAmount, request.currency || 'USD'),
            category: request.category,
            merchant: request.merchant,
            country: request.country,
            currency: request.currency || 'USD',
        };
        if (request.useCache !== false) {
            const cachedResult = await this.getCachedComparison(criteria, request.maxCacheAge);
            if (cachedResult) {
                this.logger.log('Returning cached comparison result');
                return cachedResult;
            }
        }
        const offers = await this.fetchRelevantOffers(criteria, options);
        this.logger.log(`Found ${offers.length} relevant offers`);
        const platformNames = await this.getPlatformNames(options.platformIds);
        const result = this.comparisonService.compareOffers(criteria, offers, platformNames);
        const filteredResult = this.applyResultFiltering(result, options);
        await this.cacheComparisonResult(filteredResult);
        this.logger.log(`Comparison completed with ${filteredResult.comparisons.length} results`);
        return filteredResult;
    }
    async refreshComparison(comparisonId) {
        const existingResult = await this.comparisonResultRepository.findById(comparisonId);
        if (!existingResult) {
            throw new Error(`Comparison result not found: ${comparisonId}`);
        }
        const request = {
            spendAmount: existingResult.criteria.spendAmount.amount,
            currency: existingResult.criteria.spendAmount.currency,
            category: existingResult.criteria.category,
            merchant: existingResult.criteria.merchant,
            country: existingResult.criteria.country,
            useCache: false,
        };
        return this.compareOffers(request);
    }
    async syncAndCompare(request, options = {}) {
        this.logger.log('Syncing platform data before comparison');
        const syncOptions = {
            platformIds: options.platformIds,
            maxConcurrency: 2,
            skipHealthCheck: false,
        };
        const syncResult = await this.platformIntegrationStrategy.syncAllPlatforms(syncOptions);
        this.logger.log(`Sync completed: ${syncResult.successfulSyncs}/${syncResult.totalPlatforms} platforms synced`);
        return this.compareOffers({ ...request, useCache: false }, options);
    }
    async getComparisonHistory(criteria, limit = 10) {
        return this.comparisonResultRepository.findAll({
            limit,
            notExpired: true,
        });
    }
    async cleanupExpiredComparisons() {
        const deletedCount = await this.comparisonResultRepository.deleteExpired();
        this.logger.log(`Cleaned up ${deletedCount} expired comparison results`);
        return deletedCount;
    }
    async getCachedComparison(criteria, maxCacheAge) {
        try {
            const cachedResult = await this.comparisonResultRepository.findByCriteria(criteria);
            if (!cachedResult || !cachedResult.isValid()) {
                return null;
            }
            if (maxCacheAge) {
                const ageInMinutes = (Date.now() - cachedResult.createdAt.getTime()) / (1000 * 60);
                if (ageInMinutes > maxCacheAge) {
                    return null;
                }
            }
            return cachedResult;
        }
        catch (error) {
            this.logger.warn('Failed to retrieve cached comparison', error);
            return null;
        }
    }
    async fetchRelevantOffers(criteria, options) {
        const findOptions = {
            status: options.includeInactive ? undefined : 'active',
            category: criteria.category,
            merchant: criteria.merchant,
            validAt: new Date(),
            platformId: options.platformIds?.length ? options.platformIds[0] : undefined,
            limit: options.maxResults || 1000,
            sortBy: 'cashbackRate',
            sortOrder: 'desc',
        };
        let offers = await this.offerRepository.findAll(findOptions);
        if (options.platformIds?.length) {
            offers = offers.filter(offer => options.platformIds.includes(offer.platformId));
        }
        if (options.minCashbackRate) {
            offers = offers.filter(offer => offer.cashbackRate.percentage >= options.minCashbackRate);
        }
        return offers;
    }
    async getPlatformNames(platformIds) {
        const platformNames = new Map();
        try {
            const platforms = platformIds?.length
                ? await Promise.all(platformIds.map(id => this.platformRepository.findById(id)))
                : await this.platformRepository.findAll();
            platforms
                .filter(platform => platform !== null)
                .forEach(platform => {
                platformNames.set(platform.id, platform.name);
            });
        }
        catch (error) {
            this.logger.warn('Failed to fetch platform names', error);
        }
        return platformNames;
    }
    applyResultFiltering(result, options) {
        let filteredComparisons = result.comparisons;
        if (options.maxResults && filteredComparisons.length > options.maxResults) {
            filteredComparisons = filteredComparisons.slice(0, options.maxResults);
        }
        if (filteredComparisons.length !== result.comparisons.length) {
            const newMetrics = {
                ...result.metrics,
                validOffersFound: filteredComparisons.length,
            };
            return new comparison_result_entity_1.ComparisonResult(result.id, result.criteria, filteredComparisons, newMetrics, result.status, result.createdAt, result.expiresAt);
        }
        return result;
    }
    async cacheComparisonResult(result) {
        try {
            await this.comparisonResultRepository.save(result);
        }
        catch (error) {
            this.logger.warn('Failed to cache comparison result', error);
        }
    }
};
exports.ComparisonEngineService = ComparisonEngineService;
exports.ComparisonEngineService = ComparisonEngineService = ComparisonEngineService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [comparison_service_1.ComparisonService, Object, Object, Object, platform_integration_strategy_1.PlatformIntegrationStrategy])
], ComparisonEngineService);
//# sourceMappingURL=comparison-engine.service.js.map