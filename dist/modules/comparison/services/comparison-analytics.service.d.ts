import { ComparisonResultRepository } from '../../../domain/repositories/comparison-result.repository';
import { CashbackOfferRepository } from '../../../domain/repositories/cashback-offer.repository';
export interface ComparisonAnalytics {
    totalComparisons: number;
    averageOffersPerComparison: number;
    topCategories: CategoryStats[];
    topMerchants: MerchantStats[];
    platformPerformance: PlatformStats[];
    averageProcessingTime: number;
    cacheHitRate: number;
}
export interface CategoryStats {
    category: string;
    comparisonCount: number;
    averageCashbackRate: number;
    totalOffers: number;
}
export interface MerchantStats {
    merchant: string;
    comparisonCount: number;
    averageCashbackRate: number;
    totalOffers: number;
}
export interface PlatformStats {
    platformId: string;
    platformName: string;
    totalOffers: number;
    averageCashbackRate: number;
    winRate: number;
    averageRank: number;
}
export interface TrendData {
    date: string;
    comparisons: number;
    averageCashbackRate: number;
    topCategory: string;
}
export declare class ComparisonAnalyticsService {
    private readonly comparisonResultRepository;
    private readonly offerRepository;
    private readonly logger;
    constructor(comparisonResultRepository: ComparisonResultRepository, offerRepository: CashbackOfferRepository);
    getComparisonAnalytics(startDate?: Date, endDate?: Date): Promise<ComparisonAnalytics>;
    getTrendData(days?: number, granularity?: 'day' | 'week' | 'month'): Promise<TrendData[]>;
    getPlatformComparisonReport(platformId: string): Promise<{
        totalComparisons: number;
        winRate: number;
        averageRank: number;
        averageCashbackRate: number;
        topCategories: string[];
        recentPerformance: TrendData[];
    }>;
    private getComparisonsInRange;
    private getEmptyAnalytics;
    private calculateAverageOffersPerComparison;
    private calculateCategoryStats;
    private calculateMerchantStats;
    private calculatePlatformStats;
    private calculateProcessingStats;
    private groupComparisonsByPeriod;
    private formatDateByGranularity;
    private getTopItems;
}
