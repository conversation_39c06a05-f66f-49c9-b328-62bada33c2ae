"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonOptionsDto = exports.ComparisonRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class ComparisonRequestDto {
    spendAmount;
    currency;
    category;
    merchant;
    country;
    useCache;
    maxCacheAge;
}
exports.ComparisonRequestDto = ComparisonRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Amount to spend',
        example: 100.00,
        minimum: 0.01,
    }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0.01),
    __metadata("design:type", Number)
], ComparisonRequestDto.prototype, "spendAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Currency code (ISO 4217)',
        example: 'USD',
        default: 'USD',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ComparisonRequestDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category to filter offers',
        example: 'shopping',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ComparisonRequestDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Merchant to filter offers',
        example: 'Amazon',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ComparisonRequestDto.prototype, "merchant", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Country code (ISO 3166-1 alpha-2)',
        example: 'US',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ComparisonRequestDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to use cached results',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ComparisonRequestDto.prototype, "useCache", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum cache age in minutes',
        example: 15,
        minimum: 1,
        maximum: 1440,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1440),
    __metadata("design:type", Number)
], ComparisonRequestDto.prototype, "maxCacheAge", void 0);
class ComparisonOptionsDto {
    includeInactive;
    maxResults;
    minCashbackRate;
    platformIds;
}
exports.ComparisonOptionsDto = ComparisonOptionsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include inactive offers',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ComparisonOptionsDto.prototype, "includeInactive", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum number of results to return',
        example: 10,
        minimum: 1,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ComparisonOptionsDto.prototype, "maxResults", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum cashback rate percentage',
        example: 1.0,
        minimum: 0,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ComparisonOptionsDto.prototype, "minCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Specific platform IDs to include',
        example: ['platform1', 'platform2'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ComparisonOptionsDto.prototype, "platformIds", void 0);
//# sourceMappingURL=comparison-request.dto.js.map