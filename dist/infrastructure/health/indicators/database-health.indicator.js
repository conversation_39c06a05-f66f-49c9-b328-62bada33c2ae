"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let DatabaseHealthIndicator = class DatabaseHealthIndicator extends terminus_1.HealthIndicator {
    connection;
    constructor(connection) {
        super();
        this.connection = connection;
    }
    async isHealthy(key) {
        try {
            const startTime = Date.now();
            if (this.connection.readyState !== 1) {
                throw new Error(`Database connection state: ${this.connection.readyState}`);
            }
            if (this.connection.db) {
                await this.connection.db.admin().ping();
            }
            const responseTime = Date.now() - startTime;
            const result = this.getStatus(key, true, {
                state: 'connected',
                responseTime: `${responseTime}ms`,
                database: this.connection.name,
                host: this.connection.host,
                port: this.connection.port,
            });
            return result;
        }
        catch (error) {
            const result = this.getStatus(key, false, {
                state: 'disconnected',
                error: error.message,
                database: this.connection.name || 'unknown',
            });
            throw new terminus_1.HealthCheckError('Database health check failed', result);
        }
    }
};
exports.DatabaseHealthIndicator = DatabaseHealthIndicator;
exports.DatabaseHealthIndicator = DatabaseHealthIndicator = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectConnection)()),
    __metadata("design:paramtypes", [mongoose_2.Connection])
], DatabaseHealthIndicator);
//# sourceMappingURL=database-health.indicator.js.map