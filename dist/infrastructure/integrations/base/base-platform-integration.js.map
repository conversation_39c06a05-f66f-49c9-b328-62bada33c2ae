{"version": 3, "file": "base-platform-integration.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/integrations/base/base-platform-integration.ts"], "names": [], "mappings": ";;;AAAA,2CAAwC;AAKxC,MAAsB,uBAAuB;IAMtB;IACA;IANF,MAAM,GAAG,IAAI,eAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpD,YAAY,CAAQ;IACpB,aAAa,CAAiB;IAExC,YACqB,UAAkB,EAClB,aAAoC;QADpC,eAAU,GAAV,UAAU,CAAQ;QAClB,kBAAa,GAAb,aAAa,CAAuB;IACtD,CAAC;IAMJ,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YAEhF,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,YAAY,EAAE,KAAK,CAAC,OAAO;aAC5B,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3C,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,QAAQ,iBAAiB,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YAC3G,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3C,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,QAAQ,iBAAiB,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YAC3G,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAe;YACzB,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,CAAC;SAChB,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC;YAIvC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;YACvC,MAAM,CAAC,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;YAC3C,MAAM,CAAC,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;YAC3C,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YAEtB,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,UAAU,KAAK,MAAM,CAAC,eAAe,mBAAmB,CAAC,CAAC;QAEhH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC/C,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,YAAkB;QAG5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iDAAiD,IAAI,CAAC,UAAU,6BAA6B,CAAC,CAAC;QAChH,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;QAGD,OAAO;YACL,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAChD,KAAK,EAAE,IAAI;SACZ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB;QAE1B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,qBAAqB;QAEzB,OAAO,EAAE,CAAC;IACZ,CAAC;IAES,KAAK,CAAC,kBAAkB;QAEhC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAES,KAAK,CAAC,kBAAkB,CAAC,MAAuB;QAQxD,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM;YACpB,OAAO,EAAE,CAAC;YACV,OAAO,EAAE,CAAC;SACX,CAAC;IACJ,CAAC;IAES,KAAK,CAAC,cAAc,CAC5B,QAAgB,EAChB,UAAuB,EAAE;QAEzB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,QAAQ,EAAE,CAAC;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,KAAK,CAAC;QAEpD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;QAEhE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,GAAG,OAAO;gBACV,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;oBACtD,GAAG,OAAO,CAAC,OAAO;iBACnB;aACF,CAAC,CAAC;YAEH,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACnF,CAAC;YAGD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAEnC,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,yBAAyB,OAAO,IAAI,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAES,mBAAmB,CAAC,QAAkB;QAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACxD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAExD,IAAI,SAAS,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC;gBAClC,SAAS,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;gBAC/C,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;aAC3B,CAAC;QACJ,CAAC;IACH,CAAC;IAES,KAAK,CAAC,gBAAgB,CAC9B,SAA2B,EAC3B,aAAqB,CAAC,EACtB,YAAoB,IAAI;QAExB,IAAI,SAAS,GAAU,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QAElD,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBAED,MAAM,KAAK,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,OAAO,GAAG,CAAC,wBAAwB,KAAK,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC7F,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;IAES,KAAK,CAAC,EAAU;QACxB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AA9OD,0DA8OC"}