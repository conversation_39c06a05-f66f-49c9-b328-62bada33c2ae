export declare class Money {
    private readonly _amount;
    private readonly _currency;
    constructor(_amount: number, _currency?: string);
    get amount(): number;
    get currency(): string;
    private validateAmount;
    private validateCurrency;
    add(other: Money): Money;
    subtract(other: Money): Money;
    multiply(factor: number): Money;
    divide(divisor: number): Money;
    isGreaterThan(other: Money): boolean;
    isLessThan(other: Money): boolean;
    equals(other: Money): boolean;
    private ensureSameCurrency;
    toString(): string;
    toJSON(): {
        amount: number;
        currency: string;
    };
}
