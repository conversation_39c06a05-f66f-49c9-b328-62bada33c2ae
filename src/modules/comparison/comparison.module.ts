import { Module } from '@nestjs/common';
import { ComparisonEngineService } from './services/comparison-engine.service';
import { ComparisonAnalyticsService } from './services/comparison-analytics.service';
import { ComparisonService } from '../../domain/services/comparison.service';
import { PlatformIntegrationModule } from '../../infrastructure/integrations/platform-integration.module';

@Module({
  imports: [PlatformIntegrationModule],
  providers: [
    ComparisonService,
    ComparisonEngineService,
    ComparisonAnalyticsService,
  ],
  exports: [
    ComparisonEngineService,
    ComparisonAnalyticsService,
    ComparisonService,
  ],
})
export class ComparisonModule {}
