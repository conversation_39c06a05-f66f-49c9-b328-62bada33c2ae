"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PlatformCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformCacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("./cache.service");
let PlatformCacheService = PlatformCacheService_1 = class PlatformCacheService {
    cacheService;
    logger = new common_1.Logger(PlatformCacheService_1.name);
    namespace = 'platforms';
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    async getPlatform(id) {
        return this.cacheService.get(id, this.namespace);
    }
    async setPlatform(platform) {
        await this.cacheService.set(platform.id, platform, {
            namespace: this.namespace,
            ttl: 7200,
        });
    }
    async getAllPlatforms() {
        const key = 'all';
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setAllPlatforms(platforms) {
        const key = 'all';
        await this.cacheService.set(key, platforms, {
            namespace: this.namespace,
            ttl: 3600,
        });
        const keyValuePairs = platforms.map(platform => ({
            key: platform.id,
            value: platform,
        }));
        await this.cacheService.mset(keyValuePairs, {
            namespace: this.namespace,
            ttl: 7200,
        });
    }
    async getActivePlatforms() {
        const key = 'active';
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setActivePlatforms(platforms) {
        const key = 'active';
        await this.cacheService.set(key, platforms, {
            namespace: this.namespace,
            ttl: 1800,
        });
    }
    async getPlatformsByCountry(countryCode) {
        const key = `country:${countryCode.toUpperCase()}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setPlatformsByCountry(countryCode, platforms) {
        const key = `country:${countryCode.toUpperCase()}`;
        await this.cacheService.set(key, platforms, {
            namespace: this.namespace,
            ttl: 3600,
        });
    }
    async getPlatformsByCurrency(currencyCode) {
        const key = `currency:${currencyCode.toUpperCase()}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setPlatformsByCurrency(currencyCode, platforms) {
        const key = `currency:${currencyCode.toUpperCase()}`;
        await this.cacheService.set(key, platforms, {
            namespace: this.namespace,
            ttl: 3600,
        });
    }
    async getPlatformHealth(platformId) {
        const key = `health:${platformId}`;
        return this.cacheService.get(key, `${this.namespace}:health`);
    }
    async setPlatformHealth(platformId, health) {
        const key = `health:${platformId}`;
        await this.cacheService.set(key, health, {
            namespace: `${this.namespace}:health`,
            ttl: 300,
        });
    }
    async getAllPlatformHealth() {
        const key = 'all_health';
        const cached = await this.cacheService.get(key, `${this.namespace}:health`);
        if (cached) {
            return new Map(Object.entries(cached));
        }
        return new Map();
    }
    async setAllPlatformHealth(healthMap) {
        const key = 'all_health';
        const healthRecord = Object.fromEntries(healthMap);
        await this.cacheService.set(key, healthRecord, {
            namespace: `${this.namespace}:health`,
            ttl: 300,
        });
    }
    async getPlatformCategories(platformId) {
        const key = `categories:${platformId}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setPlatformCategories(platformId, categories) {
        const key = `categories:${platformId}`;
        await this.cacheService.set(key, categories, {
            namespace: this.namespace,
            ttl: 7200,
        });
    }
    async getPlatformMerchants(platformId) {
        const key = `merchants:${platformId}`;
        const cached = await this.cacheService.get(key, this.namespace);
        return cached || [];
    }
    async setPlatformMerchants(platformId, merchants) {
        const key = `merchants:${platformId}`;
        await this.cacheService.set(key, merchants, {
            namespace: this.namespace,
            ttl: 7200,
        });
    }
    async invalidatePlatform(id) {
        await this.cacheService.del(id, this.namespace);
        await this.invalidateAllPlatforms();
        await this.invalidateActivePlatforms();
        await this.invalidatePlatformHealth(id);
    }
    async invalidateAllPlatforms() {
        await this.cacheService.del('all', this.namespace);
    }
    async invalidateActivePlatforms() {
        await this.cacheService.del('active', this.namespace);
    }
    async invalidatePlatformHealth(platformId) {
        const key = `health:${platformId}`;
        await this.cacheService.del(key, `${this.namespace}:health`);
        await this.cacheService.del('all_health', `${this.namespace}:health`);
    }
    async invalidateCountryPlatforms(countryCode) {
        const key = `country:${countryCode.toUpperCase()}`;
        await this.cacheService.del(key, this.namespace);
    }
    async invalidateCurrencyPlatforms(currencyCode) {
        const key = `currency:${currencyCode.toUpperCase()}`;
        await this.cacheService.del(key, this.namespace);
    }
    async refreshPlatformCache(platformFactory) {
        try {
            this.logger.log('Refreshing platform cache');
            const platforms = await platformFactory();
            await this.setAllPlatforms(platforms);
            const activePlatforms = platforms.filter(p => p.isActive());
            await this.setActivePlatforms(activePlatforms);
            const platformsByCountry = this.groupPlatformsByCountry(platforms);
            for (const [country, countryPlatforms] of platformsByCountry) {
                await this.setPlatformsByCountry(country, countryPlatforms);
            }
            const platformsByCurrency = this.groupPlatformsByCurrency(platforms);
            for (const [currency, currencyPlatforms] of platformsByCurrency) {
                await this.setPlatformsByCurrency(currency, currencyPlatforms);
            }
            this.logger.log(`Cached ${platforms.length} platforms`);
        }
        catch (error) {
            this.logger.error('Failed to refresh platform cache', error);
        }
    }
    groupPlatformsByCountry(platforms) {
        const grouped = new Map();
        platforms.forEach(platform => {
            platform.metadata.supportedCountries.forEach(country => {
                const existing = grouped.get(country) || [];
                existing.push(platform);
                grouped.set(country, existing);
            });
        });
        return grouped;
    }
    groupPlatformsByCurrency(platforms) {
        const grouped = new Map();
        platforms.forEach(platform => {
            platform.metadata.supportedCurrencies.forEach(currency => {
                const existing = grouped.get(currency) || [];
                existing.push(platform);
                grouped.set(currency, existing);
            });
        });
        return grouped;
    }
    async getPlatformStats() {
        return {
            totalCached: 0,
            activePlatforms: 0,
            healthyPlatforms: 0,
            byCountry: {},
            byCurrency: {},
        };
    }
};
exports.PlatformCacheService = PlatformCacheService;
exports.PlatformCacheService = PlatformCacheService = PlatformCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], PlatformCacheService);
//# sourceMappingURL=platform-cache.service.js.map