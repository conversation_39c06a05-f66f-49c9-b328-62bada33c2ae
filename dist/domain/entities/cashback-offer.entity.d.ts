import { CashbackRate } from '../value-objects/cashback-rate.vo';
import { Money } from '../value-objects/money.vo';
import { OfferStatus } from '../enums/platform-status.enum';
export interface OfferTerms {
    description: string;
    exclusions?: string[];
    validFrom: Date;
    validUntil?: Date;
    maxRedemptions?: number;
    currentRedemptions?: number;
}
export interface OfferMetadata {
    category: string;
    subcategory?: string;
    merchant: string;
    tags: string[];
    featured: boolean;
    priority: number;
}
export declare class CashbackOffer {
    private readonly _id;
    private readonly _platformId;
    private readonly _title;
    private readonly _cashbackRate;
    private readonly _terms;
    private readonly _metadata;
    private readonly _status;
    private readonly _createdAt;
    private _updatedAt;
    constructor(_id: string, _platformId: string, _title: string, _cashbackRate: CashbackRate, _terms: OfferTerms, _metadata: OfferMetadata, _status?: OfferStatus, _createdAt?: Date, _updatedAt?: Date);
    get id(): string;
    get platformId(): string;
    get title(): string;
    get cashbackRate(): CashbackRate;
    get terms(): OfferTerms;
    get metadata(): OfferMetadata;
    get status(): OfferStatus;
    get createdAt(): Date;
    get updatedAt(): Date;
    private validateId;
    private validatePlatformId;
    private validateTitle;
    private validateTerms;
    private validateMetadata;
    isActive(): boolean;
    isValid(date?: Date): boolean;
    calculateCashback(spendAmount: Money): Money;
    belongsToCategory(category: string): boolean;
    belongsToMerchant(merchant: string): boolean;
    hasTag(tag: string): boolean;
    touch(): void;
    equals(other: CashbackOffer): boolean;
    toString(): string;
}
