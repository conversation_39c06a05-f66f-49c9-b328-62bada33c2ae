"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var PlatformIntegrationStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformIntegrationStrategy = void 0;
const common_1 = require("@nestjs/common");
let PlatformIntegrationStrategy = PlatformIntegrationStrategy_1 = class PlatformIntegrationStrategy {
    logger = new common_1.Logger(PlatformIntegrationStrategy_1.name);
    integrations = new Map();
    registerIntegration(integration) {
        const platformId = integration.getPlatformId();
        this.integrations.set(platformId, integration);
        this.logger.log(`Registered integration for platform: ${platformId}`);
    }
    unregisterIntegration(platformId) {
        if (this.integrations.delete(platformId)) {
            this.logger.log(`Unregistered integration for platform: ${platformId}`);
        }
    }
    getIntegration(platformId) {
        return this.integrations.get(platformId);
    }
    getAllIntegrations() {
        return Array.from(this.integrations.values());
    }
    getRegisteredPlatformIds() {
        return Array.from(this.integrations.keys());
    }
    async syncAllPlatforms(options = {}) {
        const startTime = Date.now();
        const platformIds = options.platformIds || this.getRegisteredPlatformIds();
        const maxConcurrency = options.maxConcurrency || 3;
        this.logger.log(`Starting batch sync for ${platformIds.length} platforms with concurrency ${maxConcurrency}`);
        const results = new Map();
        let successfulSyncs = 0;
        let failedSyncs = 0;
        for (let i = 0; i < platformIds.length; i += maxConcurrency) {
            const batch = platformIds.slice(i, i + maxConcurrency);
            const batchPromises = batch.map(platformId => this.syncSinglePlatform(platformId, options));
            const batchResults = await Promise.allSettled(batchPromises);
            batchResults.forEach((result, index) => {
                const platformId = batch[index];
                if (result.status === 'fulfilled') {
                    results.set(platformId, result.value);
                    if (result.value.success) {
                        successfulSyncs++;
                    }
                    else {
                        failedSyncs++;
                    }
                }
                else {
                    failedSyncs++;
                    results.set(platformId, {
                        success: false,
                        offersProcessed: 0,
                        offersAdded: 0,
                        offersUpdated: 0,
                        offersRemoved: 0,
                        errors: [result.reason?.message || 'Unknown error'],
                        syncDuration: 0,
                    });
                }
            });
        }
        const totalDuration = Date.now() - startTime;
        this.logger.log(`Batch sync completed: ${successfulSyncs} successful, ${failedSyncs} failed, ${totalDuration}ms total`);
        return {
            totalPlatforms: platformIds.length,
            successfulSyncs,
            failedSyncs,
            results,
            totalDuration,
        };
    }
    async syncSinglePlatform(platformId, options = {}) {
        const integration = this.getIntegration(platformId);
        if (!integration) {
            throw new Error(`No integration found for platform: ${platformId}`);
        }
        if (!options.skipHealthCheck) {
            const health = await integration.checkHealth();
            if (!health.isHealthy) {
                throw new Error(`Platform ${platformId} is not healthy: ${health.errorMessage}`);
            }
        }
        if (options.forceFullSync) {
            return integration.syncOffers();
        }
        else {
            return integration.syncOffers();
        }
    }
    async fetchOffersFromAllPlatforms() {
        const results = new Map();
        const integrations = this.getAllIntegrations();
        const promises = integrations.map(async (integration) => {
            try {
                const offers = await integration.fetchOffers();
                results.set(integration.getPlatformId(), offers);
            }
            catch (error) {
                this.logger.error(`Failed to fetch offers from platform ${integration.getPlatformId()}`, error);
                results.set(integration.getPlatformId(), []);
            }
        });
        await Promise.allSettled(promises);
        return results;
    }
    async fetchOffersByCategory(category) {
        const results = new Map();
        const integrations = this.getAllIntegrations();
        const promises = integrations.map(async (integration) => {
            try {
                const offers = await integration.fetchOffersByCategory(category);
                results.set(integration.getPlatformId(), offers);
            }
            catch (error) {
                this.logger.error(`Failed to fetch offers by category from platform ${integration.getPlatformId()}`, error);
                results.set(integration.getPlatformId(), []);
            }
        });
        await Promise.allSettled(promises);
        return results;
    }
    async fetchOffersByMerchant(merchant) {
        const results = new Map();
        const integrations = this.getAllIntegrations();
        const promises = integrations.map(async (integration) => {
            try {
                const offers = await integration.fetchOffersByMerchant(merchant);
                results.set(integration.getPlatformId(), offers);
            }
            catch (error) {
                this.logger.error(`Failed to fetch offers by merchant from platform ${integration.getPlatformId()}`, error);
                results.set(integration.getPlatformId(), []);
            }
        });
        await Promise.allSettled(promises);
        return results;
    }
    async checkAllPlatformsHealth() {
        const results = new Map();
        const integrations = this.getAllIntegrations();
        const promises = integrations.map(async (integration) => {
            try {
                const health = await integration.checkHealth();
                results.set(integration.getPlatformId(), health.isHealthy);
            }
            catch (error) {
                this.logger.error(`Health check failed for platform ${integration.getPlatformId()}`, error);
                results.set(integration.getPlatformId(), false);
            }
        });
        await Promise.allSettled(promises);
        return results;
    }
    async getPlatformInfo(platformId) {
        const integration = this.getIntegration(platformId);
        if (!integration) {
            throw new Error(`No integration found for platform: ${platformId}`);
        }
        return integration.getPlatformInfo();
    }
    async getAllPlatformInfo() {
        const results = new Map();
        const integrations = this.getAllIntegrations();
        const promises = integrations.map(async (integration) => {
            try {
                const platformInfo = await integration.getPlatformInfo();
                results.set(integration.getPlatformId(), platformInfo);
            }
            catch (error) {
                this.logger.error(`Failed to get platform info for ${integration.getPlatformId()}`, error);
            }
        });
        await Promise.allSettled(promises);
        return results;
    }
};
exports.PlatformIntegrationStrategy = PlatformIntegrationStrategy;
exports.PlatformIntegrationStrategy = PlatformIntegrationStrategy = PlatformIntegrationStrategy_1 = __decorate([
    (0, common_1.Injectable)()
], PlatformIntegrationStrategy);
//# sourceMappingURL=platform-integration.strategy.js.map