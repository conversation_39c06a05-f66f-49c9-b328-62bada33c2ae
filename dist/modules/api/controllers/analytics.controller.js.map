{"version": 3, "file": "analytics.controller.js", "sourceRoot": "", "sources": ["../../../../src/modules/api/controllers/analytics.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAUwB;AACxB,6CAQyB;AACzB,qDAAuF;AACvF,yDAA8C;AAC9C,yGAAoG;AACpG,0EAIuC;AAEvC,MAAM,iBAAiB;IASrB,SAAS,CAAU;IAUnB,OAAO,CAAU;CAClB;AAXC;IARC,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,qCAAqC;QAClD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;oDACI;AAUnB;IARC,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,mCAAmC;QAChD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;kDACE;AAGnB,MAAM,aAAa;IAYjB,IAAI,CAAU;IAWd,WAAW,CAA4B;CACxC;AAZC;IAXC,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,yCAAyC;QACtD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;IACR,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;;2CAChC;AAWd;IATC,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,2BAA2B;QACxC,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;KAC/B,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;;kDACM;AAMlC,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAIX;IAHF,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAE/D,YACmB,gBAA4C;QAA5C,qBAAgB,GAAhB,gBAAgB,CAA4B;IAC5D,CAAC;IAkBE,AAAN,KAAK,CAAC,oBAAoB,CACf,KAAwB;QAEjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAEjD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC1E,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAEpE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAClE,SAAS,EACT,OAAO,CACR,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,IAAI,sBAAa,CACrB,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAChD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAkBK,AAAN,KAAK,CAAC,YAAY,CACP,KAAoB;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,KAAK,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;YAEtE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CACxD,KAAK,CAAC,IAAI,IAAI,EAAE,EAChB,KAAK,CAAC,WAAW,IAAI,KAAK,CAC3B,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,sBAAa,CACrB,kCAAkC,KAAK,CAAC,OAAO,EAAE,EACjD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAwBK,AAAN,KAAK,CAAC,oBAAoB,CACH,UAAkB;QAEvC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,UAAU,EAAE,CAAC,CAAC;YAEnE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;YAEnF,IAAI,MAAM,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,sBAAa,CACrB,+BAA+B,UAAU,EAAE,EAC3C,mBAAU,CAAC,SAAS,CACrB,CAAC;YACJ,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YAEpF,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,sBAAa,CACrB,0CAA0C,KAAK,CAAC,OAAO,EAAE,EACzD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAiBK,AAAN,KAAK,CAAC,gBAAgB,CACJ,KAAc;QAE9B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAE1C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;YAEvE,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,sBAAa,CACrB,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAChD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAiBK,AAAN,KAAK,CAAC,eAAe,CACH,KAAc;QAE9B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAEzC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;YAEvE,OAAO,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,sBAAa,CACrB,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAC/C,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAWK,AAAN,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAErD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;YAEvE,OAAO,SAAS,CAAC,mBAAmB,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,IAAI,sBAAa,CACrB,4CAA4C,KAAK,CAAC,OAAO,EAAE,EAC3D,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAlOY,kDAAmB;AAuBxB;IAhBL,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mCAAmC;QAC5C,WAAW,EAAE,0EAA0E;KACxF,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,2CAA2C;QACxD,IAAI,EAAE,+CAAsB;KAC7B,CAAC;IACD,IAAA,+BAAqB,EAAC;QACrB,WAAW,EAAE,0BAA0B;KACxC,CAAC;IACD,IAAA,wCAA8B,EAAC;QAC9B,WAAW,EAAE,kDAAkD;KAChE,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;;qCAAQ,iBAAiB;;+DAqBlC;AAkBK;IAhBL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,+CAA+C;KAC7D,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,mCAAmC;QAChD,IAAI,EAAE,CAAC,qCAAY,CAAC;KACrB,CAAC;IACD,IAAA,+BAAqB,EAAC;QACrB,WAAW,EAAE,0BAA0B;KACxC,CAAC;IACD,IAAA,wCAA8B,EAAC;QAC9B,WAAW,EAAE,mDAAmD;KACjE,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;;qCAAQ,aAAa;;uDAkB9B;AAwBK;IAtBL,IAAA,YAAG,EAAC,uBAAuB,CAAC;IAC5B,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,iCAAiC;QAC1C,WAAW,EAAE,qDAAqD;KACnE,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,iBAAiB;KAC3B,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,2CAA2C;QACxD,IAAI,EAAE,0CAAiB;KACxB,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,SAAS;QAC5B,WAAW,EAAE,oBAAoB;KAClC,CAAC;IACD,IAAA,wCAA8B,EAAC;QAC9B,WAAW,EAAE,2DAA2D;KACzE,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;;;;+DA2BrB;AAiBK;IAfL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,oBAAoB;QAC7B,WAAW,EAAE,0DAA0D;KACxE,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,wCAAwC;QACrD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,uCAAuC;KACrD,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;;2DAehB;AAiBK;IAfL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mBAAmB;QAC5B,WAAW,EAAE,yDAAyD;KACvE,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,uCAAuC;QACpD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,sCAAsC;KACpD,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;;0DAehB;AAWK;IATL,IAAA,YAAG,EAAC,uBAAuB,CAAC;IAC5B,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,qCAAqC;QAC9C,WAAW,EAAE,kDAAkD;KAChE,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,kDAAkD;KAChE,CAAC;;;;iEAeD;8BAjOU,mBAAmB;IAH/B,IAAA,iBAAO,EAAC,WAAW,CAAC;IACpB,IAAA,mBAAU,EAAC,WAAW,CAAC;IACvB,IAAA,iBAAQ,EAAC,IAAI,uBAAc,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;qCAKX,yDAA0B;GAJpD,mBAAmB,CAkO/B"}