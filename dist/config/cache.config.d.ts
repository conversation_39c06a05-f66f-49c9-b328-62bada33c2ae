declare const _default: (() => {
    redis: {
        host: string;
        port: number;
        password: string | undefined;
        db: number;
        keyPrefix: string;
        maxRetriesPerRequest: number;
        retryDelayOnFailover: number;
        enableReadyCheck: boolean;
    };
    ttl: {
        comparison: number;
        offers: number;
        platforms: number;
        analytics: number;
        health: number;
    };
    maxItems: {
        comparison: number;
        offers: number;
        platforms: number;
        analytics: number;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    redis: {
        host: string;
        port: number;
        password: string | undefined;
        db: number;
        keyPrefix: string;
        maxRetriesPerRequest: number;
        retryDelayOnFailover: number;
        enableReadyCheck: boolean;
    };
    ttl: {
        comparison: number;
        offers: number;
        platforms: number;
        analytics: number;
        health: number;
    };
    maxItems: {
        comparison: number;
        offers: number;
        platforms: number;
        analytics: number;
    };
}>;
export default _default;
