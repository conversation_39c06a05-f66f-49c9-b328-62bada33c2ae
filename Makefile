# Makefile for Cashback Comparison Platform

.PHONY: help install build start start-dev stop clean test lint format docker-build docker-up docker-down logs

# Default target
help:
	@echo "Available commands:"
	@echo "  install          - Install dependencies"
	@echo "  build            - Build the application"
	@echo "  start            - Start the application in production mode"
	@echo "  start-dev        - Start the application in development mode"
	@echo "  dev              - Run application locally (requires databases)"
	@echo "  dev-full         - Start databases and run application locally"
	@echo "  start-databases  - Start MongoDB and Redis containers"
	@echo "  stop-databases   - Stop database containers"
	@echo "  stop             - Stop the application"
	@echo "  clean            - Clean build artifacts and dependencies"
	@echo "  test             - Run unit tests"
	@echo "  test-api         - Run API integration tests"
	@echo "  lint             - Run linter"
	@echo "  format           - Format code"
	@echo "  docker-build     - Build Docker images"
	@echo "  docker-up        - Start services with Docker Compose"
	@echo "  docker-down      - Stop Docker services"
	@echo "  logs             - View application logs"

# Development commands
install:
	npm install

build:
	npm run build

start:
	npm run start:prod

start-dev:
	npm run start:dev

stop:
	pkill -f "node.*dist/main" || true

clean:
	rm -rf dist node_modules logs/*.log

test:
	npm run test

test-e2e:
	npm run test:e2e

test-cov:
	npm run test:cov

test-api:
	@echo "Running API integration tests..."
	./test-api.sh

lint:
	npm run lint

format:
	npm run format

# Docker commands
docker-build:
	docker-compose build

docker-up:
	docker-compose up -d

docker-up-dev:
	docker-compose -f docker-compose.dev.yml up -d

docker-down:
	docker-compose down

docker-down-dev:
	docker-compose -f docker-compose.dev.yml down

docker-logs:
	docker-compose logs -f app

docker-clean:
	docker-compose down -v
	docker system prune -f

# Database commands
db-seed:
	npm run db:seed

db-migrate:
	npm run db:migrate

db-reset:
	npm run db:reset

# Utility commands
logs:
	tail -f logs/app.log

logs-error:
	tail -f logs/error.log

health:
	curl -f http://localhost:3000/api/v1/health || echo "Service is not healthy"

# Production deployment
deploy-prod:
	@echo "Deploying to production..."
	docker-compose -f docker-compose.yml up -d --build

# Development setup
setup-dev:
	@echo "Setting up development environment..."
	cp .env.development .env
	npm install
	docker-compose -f docker-compose.dev.yml up -d mongo redis
	@echo "Development environment ready!"

# Start only databases for local development
start-databases:
	@echo "Starting MongoDB and Redis containers..."
	@docker run -d --name mongo-dev -p 27017:27017 mongo:6.0 2>/dev/null || echo "MongoDB container already running"
	@docker run -d --name redis-dev -p 6379:6379 redis:7-alpine 2>/dev/null || echo "Redis container already running"
	@echo "Databases started successfully!"

# Stop databases
stop-databases:
	@echo "Stopping database containers..."
	@docker stop mongo-dev redis-dev 2>/dev/null || true
	@docker rm mongo-dev redis-dev 2>/dev/null || true
	@echo "Databases stopped successfully!"

# Run application locally (requires databases to be running)
dev:
	@echo "Starting application in development mode..."
	@echo "Make sure databases are running with: make start-databases"
	npm run start:dev

# Full local development setup
dev-full: start-databases
	@echo "Waiting for databases to be ready..."
	@sleep 5
	@echo "Starting application..."
	npm run start:dev

# CI/CD helpers
ci-test:
	npm ci
	npm run test
	npm run test:e2e

ci-build:
	npm run build
	docker build -t cashback-comparison-platform .

# Monitoring
monitor:
	@echo "Application Status:"
	@curl -s http://localhost:3000/api/v1/health || echo "Health check failed"
	@echo "\nDatabase Status:"
	@curl -s http://localhost:3000/api/v1/health/database || echo "Database check failed"
	@echo "\nCache Status:"
	@curl -s http://localhost:3000/api/v1/health/cache || echo "Cache check failed"
	@echo "\nDocker Services:"
	@docker ps --filter "name=mongo-dev" --filter "name=redis-dev" || echo "No development containers running"
