declare const _default: (() => {
    uri: string;
    testUri: string;
    options: {
        maxPoolSize: number;
        serverSelectionTimeoutMS: number;
        socketTimeoutMS: number;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    uri: string;
    testUri: string;
    options: {
        maxPoolSize: number;
        serverSelectionTimeoutMS: number;
        socketTimeoutMS: number;
    };
}>;
export default _default;
