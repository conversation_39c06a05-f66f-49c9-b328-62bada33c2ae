declare const _default: (() => {
    port: number;
    nodeEnv: string;
    apiPrefix: string;
    cache: {
        ttl: number;
        maxItems: number;
    };
    throttle: {
        ttl: number;
        limit: number;
    };
    logging: {
        level: string;
        filePath: string;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    port: number;
    nodeEnv: string;
    apiPrefix: string;
    cache: {
        ttl: number;
        maxItems: number;
    };
    throttle: {
        ttl: number;
        limit: number;
    };
    logging: {
        level: string;
        filePath: string;
    };
}>;
export default _default;
