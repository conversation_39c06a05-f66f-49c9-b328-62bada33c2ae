"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryModule = exports.COMPARISON_RESULT_REPOSITORY = exports.CASHBACK_PLATFORM_REPOSITORY = exports.CASHBACK_OFFER_REPOSITORY = void 0;
const common_1 = require("@nestjs/common");
const mock_cashback_offer_repository_1 = require("./mock-cashback-offer.repository");
const mock_cashback_platform_repository_1 = require("./mock-cashback-platform.repository");
const mock_comparison_result_repository_1 = require("./mock-comparison-result.repository");
exports.CASHBACK_OFFER_REPOSITORY = 'CASHBACK_OFFER_REPOSITORY';
exports.CASHBACK_PLATFORM_REPOSITORY = 'CASHBACK_PLATFORM_REPOSITORY';
exports.COMPARISON_RESULT_REPOSITORY = 'COMPARISON_RESULT_REPOSITORY';
let RepositoryModule = class RepositoryModule {
};
exports.RepositoryModule = RepositoryModule;
exports.RepositoryModule = RepositoryModule = __decorate([
    (0, common_1.Module)({
        providers: [
            {
                provide: exports.CASHBACK_OFFER_REPOSITORY,
                useClass: mock_cashback_offer_repository_1.MockCashbackOfferRepository,
            },
            {
                provide: exports.CASHBACK_PLATFORM_REPOSITORY,
                useClass: mock_cashback_platform_repository_1.MockCashbackPlatformRepository,
            },
            {
                provide: exports.COMPARISON_RESULT_REPOSITORY,
                useClass: mock_comparison_result_repository_1.MockComparisonResultRepository,
            },
        ],
        exports: [
            exports.CASHBACK_OFFER_REPOSITORY,
            exports.CASHBACK_PLATFORM_REPOSITORY,
            exports.COMPARISON_RESULT_REPOSITORY,
        ],
    })
], RepositoryModule);
//# sourceMappingURL=repository.module.js.map