{"version": 3, "file": "sample-platform.integration.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/integrations/implementations/sample-platform.integration.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,iFAA4E;AAC5E,0FAA+E;AAC/E,gGAA8H;AAC9H,qFAA8E;AAC9E,qFAAyF;AA2BlF,IAAM,yBAAyB,GAA/B,MAAM,yBAA0B,SAAQ,mDAAuB;IACpE;QACE,MAAM,aAAa,GAA0B;YAC3C,WAAW,EAAE,6CAA6C;YAC1D,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB;YAC3C,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,KAAK;YACd,aAAa,EAAE,CAAC;SACjB,CAAC;QAEF,KAAK,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,MAAM,QAAQ,GAAqB;YACjC,OAAO,EAAE,sCAAsC;YAC/C,WAAW,EAAE,qDAAqD;YAClE,kBAAkB,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACtC,mBAAmB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAC1C,UAAU,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,CAAC;SAC3E,CAAC;QAEF,OAAO,IAAI,2CAAgB,CACzB,IAAI,CAAC,UAAU,EACf,0BAA0B,EAC1B,qCAAc,CAAC,MAAM,EACrB,QAAQ,EACR,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBACtD,OAAO,IAAI,CAAC,cAAc,CAAoB,SAAS,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBACtD,OAAO,IAAI,CAAC,cAAc,CAAoB,oBAAoB,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,QAAQ,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAChG,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;gBACtD,OAAO,IAAI,CAAC,cAAc,CAAoB,oBAAoB,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,QAAQ,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAChG,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,cAAc,CAAqB,SAAS,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAA2B,aAAa,CAAC,CAAC;YACpF,OAAO,QAAQ,CAAC,UAAU,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2DAA2D,EAAE,KAAK,CAAC,CAAC;YAEtF,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAA0B,YAAY,CAAC,CAAC;YAClF,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YACrF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAES,KAAK,CAAC,kBAAkB;QAChC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAwC,SAAS,CAAC,CAAC;YAE7F,IAAI,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,QAAwB;QAClD,MAAM,YAAY,GAAG,IAAI,+BAAY,CACnC,QAAQ,CAAC,mBAAmB,EAC5B,QAAQ,CAAC,YAAY,EACrB,QAAQ,CAAC,SAAS,CACnB,CAAC;QAEF,MAAM,KAAK,GAAG;YACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,SAAS,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACxC,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;SAC9E,CAAC;QAEF,MAAM,QAAQ,GAAG;YACf,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;SAC5B,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE/D,OAAO,IAAI,qCAAa,CACtB,GAAG,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,EAAE,EAAE,EACnC,IAAI,CAAC,UAAU,EACf,QAAQ,CAAC,KAAK,EACd,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,MAAM,CACP,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,SAAiB;QACjD,QAAQ,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;YAChC,KAAK,QAAQ;gBACX,OAAO,kCAAW,CAAC,MAAM,CAAC;YAC5B,KAAK,SAAS;gBACZ,OAAO,kCAAW,CAAC,OAAO,CAAC;YAC7B,KAAK,aAAa;gBAChB,OAAO,kCAAW,CAAC,WAAW,CAAC;YACjC,KAAK,QAAQ;gBACX,OAAO,kCAAW,CAAC,MAAM,CAAC;YAC5B;gBACE,OAAO,kCAAW,CAAC,MAAM,CAAC;QAC9B,CAAC;IACH,CAAC;CACF,CAAA;AAzKY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;;GACA,yBAAyB,CAyKrC"}