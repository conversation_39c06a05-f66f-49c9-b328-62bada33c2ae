import { OnModuleInit } from '@nestjs/common';
import { PlatformIntegrationStrategy } from './strategy/platform-integration.strategy';
import { SamplePlatformIntegration } from './implementations/sample-platform.integration';
export declare class PlatformIntegrationModule implements OnModuleInit {
    private readonly integrationStrategy;
    private readonly samplePlatformIntegration;
    constructor(integrationStrategy: PlatformIntegrationStrategy, samplePlatformIntegration: SamplePlatformIntegration);
    onModuleInit(): Promise<void>;
}
