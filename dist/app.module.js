"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const database_module_1 = require("./database/database.module");
const api_module_1 = require("./modules/api/api.module");
const cache_module_1 = require("./infrastructure/cache/cache.module");
const logging_module_1 = require("./infrastructure/logging/logging.module");
const health_module_1 = require("./infrastructure/health/health.module");
const database_config_1 = require("./config/database.config");
const app_config_1 = require("./config/app.config");
const cache_config_1 = require("./config/cache.config");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [database_config_1.default, app_config_1.default, cache_config_1.default],
                envFilePath: ['.env.local', '.env'],
            }),
            logging_module_1.LoggingModule,
            database_module_1.DatabaseModule,
            cache_module_1.CacheModule,
            health_module_1.HealthModule,
            api_module_1.ApiModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map