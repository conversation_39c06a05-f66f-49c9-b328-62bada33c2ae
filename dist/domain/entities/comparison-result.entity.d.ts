import { Money } from '../value-objects/money.vo';
import { CashbackOffer } from './cashback-offer.entity';
import { ComparisonStatus } from '../enums/platform-status.enum';
export interface ComparisonCriteria {
    spendAmount: Money;
    category?: string;
    merchant?: string;
    country?: string;
    currency?: string;
}
export interface OfferComparison {
    offer: CashbackOffer;
    platformName: string;
    estimatedCashback: Money;
    rank: number;
    score: number;
}
export interface ComparisonMetrics {
    totalOffersEvaluated: number;
    validOffersFound: number;
    averageCashbackRate: number;
    bestCashbackRate: number;
    processingTimeMs: number;
}
export declare class ComparisonResult {
    private readonly _id;
    private readonly _criteria;
    private readonly _comparisons;
    private readonly _metrics;
    private readonly _status;
    private readonly _createdAt;
    private _expiresAt;
    constructor(_id: string, _criteria: ComparisonCriteria, _comparisons: OfferComparison[], _metrics: ComparisonMetrics, _status?: ComparisonStatus, _createdAt?: Date, _expiresAt?: Date);
    get id(): string;
    get criteria(): ComparisonCriteria;
    get comparisons(): OfferComparison[];
    get metrics(): ComparisonMetrics;
    get status(): ComparisonStatus;
    get createdAt(): Date;
    get expiresAt(): Date;
    private validateId;
    private validateCriteria;
    private validateComparisons;
    private sortComparisons;
    getBestOffer(): OfferComparison | null;
    getTopOffers(count?: number): OfferComparison[];
    getOffersByPlatform(platformName: string): OfferComparison[];
    getOffersByCategory(category: string): OfferComparison[];
    getTotalPotentialSavings(): Money;
    isExpired(): boolean;
    isValid(): boolean;
    extendExpiry(additionalMinutes: number): void;
    equals(other: ComparisonResult): boolean;
    toString(): string;
}
