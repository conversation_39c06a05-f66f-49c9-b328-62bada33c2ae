import { HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { PlatformIntegrationStrategy } from '../../integrations/strategy/platform-integration.strategy';
export declare class PlatformHealthIndicator extends HealthIndicator {
    private platformIntegrationStrategy;
    constructor(platformIntegrationStrategy: PlatformIntegrationStrategy);
    isHealthy(key: string): Promise<HealthIndicatorResult>;
}
