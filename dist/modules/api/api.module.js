"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiModule = void 0;
const common_1 = require("@nestjs/common");
const comparison_controller_1 = require("./controllers/comparison.controller");
const analytics_controller_1 = require("./controllers/analytics.controller");
const comparison_mapper_1 = require("./mappers/comparison.mapper");
const comparison_module_1 = require("../comparison/comparison.module");
let ApiModule = class ApiModule {
};
exports.ApiModule = ApiModule;
exports.ApiModule = ApiModule = __decorate([
    (0, common_1.Module)({
        imports: [comparison_module_1.ComparisonModule],
        controllers: [
            comparison_controller_1.ComparisonController,
            analytics_controller_1.AnalyticsController,
        ],
        providers: [
            comparison_mapper_1.ComparisonMapper,
        ],
        exports: [
            comparison_mapper_1.ComparisonMapper,
        ],
    })
], ApiModule);
//# sourceMappingURL=api.module.js.map