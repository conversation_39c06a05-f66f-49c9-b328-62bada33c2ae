import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MoneyDto {
  @ApiProperty({
    description: 'Amount',
    example: 5.50,
  })
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
  })
  currency: string;
}

export class CashbackRateDto {
  @ApiProperty({
    description: 'Cashback percentage',
    example: 5.5,
  })
  percentage: number;

  @ApiPropertyOptional({
    description: 'Maximum cashback amount',
    example: 25.00,
  })
  maxAmount?: number;

  @ApiPropertyOptional({
    description: 'Minimum spend required',
    example: 50.00,
  })
  minSpend?: number;
}

export class OfferTermsDto {
  @ApiProperty({
    description: 'Offer description',
    example: '5% cashback on all purchases',
  })
  description: string;

  @ApiPropertyOptional({
    description: 'Offer exclusions',
    example: ['Gift cards', 'Shipping fees'],
  })
  exclusions?: string[];

  @ApiProperty({
    description: 'Valid from date',
    example: '2024-01-01T00:00:00Z',
  })
  validFrom: string;

  @ApiPropertyOptional({
    description: 'Valid until date',
    example: '2024-12-31T23:59:59Z',
  })
  validUntil?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of redemptions',
    example: 1000,
  })
  maxRedemptions?: number;

  @ApiPropertyOptional({
    description: 'Current number of redemptions',
    example: 250,
  })
  currentRedemptions?: number;
}

export class OfferMetadataDto {
  @ApiProperty({
    description: 'Offer category',
    example: 'shopping',
  })
  category: string;

  @ApiPropertyOptional({
    description: 'Offer subcategory',
    example: 'electronics',
  })
  subcategory?: string;

  @ApiProperty({
    description: 'Merchant name',
    example: 'Amazon',
  })
  merchant: string;

  @ApiProperty({
    description: 'Offer tags',
    example: ['popular', 'limited-time'],
  })
  tags: string[];

  @ApiProperty({
    description: 'Whether the offer is featured',
    example: true,
  })
  featured: boolean;

  @ApiProperty({
    description: 'Offer priority (1-10)',
    example: 8,
  })
  priority: number;
}

export class CashbackOfferDto {
  @ApiProperty({
    description: 'Offer ID',
    example: 'platform1_offer123',
  })
  id: string;

  @ApiProperty({
    description: 'Platform ID',
    example: 'platform1',
  })
  platformId: string;

  @ApiProperty({
    description: 'Offer title',
    example: '5% Cashback at Amazon',
  })
  title: string;

  @ApiProperty({
    description: 'Cashback rate details',
    type: CashbackRateDto,
  })
  cashbackRate: CashbackRateDto;

  @ApiProperty({
    description: 'Offer terms and conditions',
    type: OfferTermsDto,
  })
  terms: OfferTermsDto;

  @ApiProperty({
    description: 'Offer metadata',
    type: OfferMetadataDto,
  })
  metadata: OfferMetadataDto;

  @ApiProperty({
    description: 'Offer status',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update date',
    example: '2024-01-01T00:00:00Z',
  })
  updatedAt: string;
}

export class OfferComparisonDto {
  @ApiProperty({
    description: 'Cashback offer details',
    type: CashbackOfferDto,
  })
  offer: CashbackOfferDto;

  @ApiProperty({
    description: 'Platform name',
    example: 'Sample Cashback Platform',
  })
  platformName: string;

  @ApiProperty({
    description: 'Estimated cashback amount',
    type: MoneyDto,
  })
  estimatedCashback: MoneyDto;

  @ApiProperty({
    description: 'Rank in comparison (1 = best)',
    example: 1,
  })
  rank: number;

  @ApiProperty({
    description: 'Comparison score (0-1)',
    example: 0.95,
  })
  score: number;
}

export class ComparisonMetricsDto {
  @ApiProperty({
    description: 'Total offers evaluated',
    example: 150,
  })
  totalOffersEvaluated: number;

  @ApiProperty({
    description: 'Valid offers found',
    example: 25,
  })
  validOffersFound: number;

  @ApiProperty({
    description: 'Average cashback rate',
    example: 3.2,
  })
  averageCashbackRate: number;

  @ApiProperty({
    description: 'Best cashback rate found',
    example: 8.5,
  })
  bestCashbackRate: number;

  @ApiProperty({
    description: 'Processing time in milliseconds',
    example: 245,
  })
  processingTimeMs: number;
}

export class ComparisonCriteriaDto {
  @ApiProperty({
    description: 'Spend amount',
    type: MoneyDto,
  })
  spendAmount: MoneyDto;

  @ApiPropertyOptional({
    description: 'Category filter',
    example: 'shopping',
  })
  category?: string;

  @ApiPropertyOptional({
    description: 'Merchant filter',
    example: 'Amazon',
  })
  merchant?: string;

  @ApiPropertyOptional({
    description: 'Country filter',
    example: 'US',
  })
  country?: string;

  @ApiPropertyOptional({
    description: 'Currency filter',
    example: 'USD',
  })
  currency?: string;
}

export class ComparisonResultDto {
  @ApiProperty({
    description: 'Comparison result ID',
    example: 'comp_1640995200000_abc123',
  })
  id: string;

  @ApiProperty({
    description: 'Comparison criteria',
    type: ComparisonCriteriaDto,
  })
  criteria: ComparisonCriteriaDto;

  @ApiProperty({
    description: 'Offer comparisons',
    type: [OfferComparisonDto],
  })
  comparisons: OfferComparisonDto[];

  @ApiProperty({
    description: 'Comparison metrics',
    type: ComparisonMetricsDto,
  })
  metrics: ComparisonMetricsDto;

  @ApiProperty({
    description: 'Comparison status',
    example: 'completed',
  })
  status: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Expiration date',
    example: '2024-01-01T00:15:00Z',
  })
  expiresAt: string;
}
