{"version": 3, "file": "platform-health.indicator.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/health/indicators/platform-health.indicator.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,+CAA4F;AAC5F,6GAAwG;AAGjG,IAAM,uBAAuB,GAA7B,MAAM,uBAAwB,SAAQ,0BAAe;IACtC;IAApB,YAAoB,2BAAwD;QAC1E,KAAK,EAAE,CAAC;QADU,gCAA2B,GAA3B,2BAA2B,CAA6B;IAE5E,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAW;QACzB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAG7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,EAAE,CAAC;YAEnF,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC;YACtC,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAC/E,MAAM,iBAAiB,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAE/F,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG5C,MAAM,SAAS,GAAG,iBAAiB,IAAI,EAAE,CAAC;YAE1C,MAAM,eAAe,GAA4B,EAAE,CAAC;YACpD,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE;gBACxC,eAAe,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE;gBAC5C,cAAc;gBACd,gBAAgB;gBAChB,iBAAiB,EAAE,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBACrD,YAAY,EAAE,GAAG,YAAY,IAAI;gBACjC,SAAS,EAAE,eAAe;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,2BAAgB,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;YACrE,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;gBACxC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc,EAAE,CAAC;gBACjB,gBAAgB,EAAE,CAAC;aACpB,CAAC,CAAC;YAEH,MAAM,IAAI,2BAAgB,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF,CAAA;AAjDY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAEsC,2DAA2B;GADjE,uBAAuB,CAiDnC"}