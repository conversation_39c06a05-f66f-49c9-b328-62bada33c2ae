import { CashbackOffer } from '../entities/cashback-offer.entity';
import { OfferStatus } from '../enums/platform-status.enum';
export interface FindOffersOptions {
    platformId?: string;
    status?: OfferStatus;
    category?: string;
    merchant?: string;
    tags?: string[];
    validAt?: Date;
    featured?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: 'cashbackRate' | 'validUntil' | 'priority' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
}
export interface CashbackOfferRepository {
    findById(id: string): Promise<CashbackOffer | null>;
    findByPlatform(platformId: string, options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findAll(options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findActive(options?: Omit<FindOffersOptions, 'status'>): Promise<CashbackOffer[]>;
    findByCategory(category: string, options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findByMerchant(merchant: string, options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findFeatured(options?: FindOffersOptions): Promise<CashbackOffer[]>;
    save(offer: CashbackOffer): Promise<CashbackOffer>;
    saveMany(offers: CashbackOffer[]): Promise<CashbackOffer[]>;
    update(id: string, offer: Partial<CashbackOffer>): Promise<CashbackOffer>;
    delete(id: string): Promise<void>;
    deleteByPlatform(platformId: string): Promise<void>;
    count(options?: FindOffersOptions): Promise<number>;
    exists(id: string): Promise<boolean>;
}
