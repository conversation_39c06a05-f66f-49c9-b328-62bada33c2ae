"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var MockComparisonResultRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockComparisonResultRepository = void 0;
const common_1 = require("@nestjs/common");
let MockComparisonResultRepository = MockComparisonResultRepository_1 = class MockComparisonResultRepository {
    logger = new common_1.Logger(MockComparisonResultRepository_1.name);
    results = [];
    async findById(id) {
        return this.results.find(result => result.id === id) || null;
    }
    async findByCriteria(criteria) {
        return this.results.find(result => result.criteria.spendAmount.amount === criteria.spendAmount.amount &&
            result.criteria.spendAmount.currency === criteria.spendAmount.currency &&
            result.criteria.category === criteria.category &&
            result.criteria.merchant === criteria.merchant &&
            result.criteria.country === criteria.country &&
            result.isValid()) || null;
    }
    async findAll(options) {
        let filtered = [...this.results];
        if (options?.status) {
            filtered = filtered.filter(result => result.status === options.status);
        }
        if (options?.createdAfter) {
            filtered = filtered.filter(result => result.createdAt >= options.createdAfter);
        }
        if (options?.createdBefore) {
            filtered = filtered.filter(result => result.createdAt <= options.createdBefore);
        }
        if (options?.notExpired) {
            filtered = filtered.filter(result => !result.isExpired());
        }
        filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        if (options?.limit) {
            filtered = filtered.slice(options.offset || 0, (options.offset || 0) + options.limit);
        }
        return filtered;
    }
    async findValid() {
        return this.results.filter(result => result.isValid());
    }
    async save(result) {
        const existingIndex = this.results.findIndex(r => r.id === result.id);
        if (existingIndex >= 0) {
            this.results[existingIndex] = result;
        }
        else {
            this.results.push(result);
        }
        this.logger.debug(`Saved comparison result: ${result.id}`);
        return result;
    }
    async update(id, result) {
        const existing = await this.findById(id);
        if (!existing) {
            throw new Error(`Comparison result not found: ${id}`);
        }
        const updated = { ...existing, ...result };
        return this.save(updated);
    }
    async delete(id) {
        const index = this.results.findIndex(result => result.id === id);
        if (index >= 0) {
            this.results.splice(index, 1);
            this.logger.debug(`Deleted comparison result: ${id}`);
        }
    }
    async deleteExpired() {
        const initialCount = this.results.length;
        this.results = this.results.filter(result => !result.isExpired());
        const deletedCount = initialCount - this.results.length;
        if (deletedCount > 0) {
            this.logger.debug(`Deleted ${deletedCount} expired comparison results`);
        }
        return deletedCount;
    }
    async count(options) {
        const filtered = await this.findAll(options);
        return filtered.length;
    }
    async exists(id) {
        return this.results.some(result => result.id === id);
    }
};
exports.MockComparisonResultRepository = MockComparisonResultRepository;
exports.MockComparisonResultRepository = MockComparisonResultRepository = MockComparisonResultRepository_1 = __decorate([
    (0, common_1.Injectable)()
], MockComparisonResultRepository);
//# sourceMappingURL=mock-comparison-result.repository.js.map