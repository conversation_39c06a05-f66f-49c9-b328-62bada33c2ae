import { Logger } from '@nestjs/common';
import { PlatformIntegration, PlatformHealthStatus, SyncResult, RateLimitInfo } from '../interfaces/platform-integration.interface';
import { CashbackOffer } from '../../../domain/entities/cashback-offer.entity';
import { CashbackPlatform, PlatformConfiguration } from '../../../domain/entities/cashback-platform.entity';
export declare abstract class BasePlatformIntegration implements PlatformIntegration {
    protected readonly platformId: string;
    protected readonly configuration: PlatformConfiguration;
    protected readonly logger: Logger;
    protected lastSyncTime?: Date;
    protected rateLimitInfo?: RateLimitInfo;
    constructor(platformId: string, configuration: PlatformConfiguration);
    abstract getPlatformInfo(): Promise<CashbackPlatform>;
    abstract fetchOffers(): Promise<CashbackOffer[]>;
    abstract validateConfiguration(): Promise<boolean>;
    getPlatformId(): string;
    checkHealth(): Promise<PlatformHealthStatus>;
    fetchOffersByCategory(category: string): Promise<CashbackOffer[]>;
    fetchOffersByMerchant(merchant: string): Promise<CashbackOffer[]>;
    syncOffers(): Promise<SyncResult>;
    syncOffersIncremental(lastSyncTime: Date): Promise<SyncResult>;
    getRateLimitInfo(): Promise<RateLimitInfo>;
    getSupportedCategories(): Promise<string[]>;
    getSupportedMerchants(): Promise<string[]>;
    protected performHealthCheck(): Promise<void>;
    protected processSyncResults(offers: CashbackOffer[]): Promise<{
        added: number;
        updated: number;
        removed: number;
    }>;
    protected makeApiRequest<T>(endpoint: string, options?: RequestInit): Promise<T>;
    protected updateRateLimitInfo(response: Response): void;
    protected retryWithBackoff<T>(operation: () => Promise<T>, maxRetries?: number, baseDelay?: number): Promise<T>;
    protected sleep(ms: number): Promise<void>;
}
