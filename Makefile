# Makefile for Cashback Comparison Platform

.PHONY: help install build start start-dev stop clean test lint format docker-build docker-up docker-down logs

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  build       - Build the application"
	@echo "  start       - Start the application in production mode"
	@echo "  start-dev   - Start the application in development mode"
	@echo "  stop        - Stop the application"
	@echo "  clean       - Clean build artifacts and dependencies"
	@echo "  test        - Run tests"
	@echo "  lint        - Run linter"
	@echo "  format      - Format code"
	@echo "  docker-build - Build Docker images"
	@echo "  docker-up   - Start services with Docker Compose"
	@echo "  docker-down - Stop Docker services"
	@echo "  logs        - View application logs"

# Development commands
install:
	npm install

build:
	npm run build

start:
	npm run start:prod

start-dev:
	npm run start:dev

stop:
	pkill -f "node.*dist/main" || true

clean:
	rm -rf dist node_modules logs/*.log

test:
	npm run test

test-e2e:
	npm run test:e2e

test-cov:
	npm run test:cov

lint:
	npm run lint

format:
	npm run format

# Docker commands
docker-build:
	docker-compose build

docker-up:
	docker-compose up -d

docker-up-dev:
	docker-compose -f docker-compose.dev.yml up -d

docker-down:
	docker-compose down

docker-down-dev:
	docker-compose -f docker-compose.dev.yml down

docker-logs:
	docker-compose logs -f app

docker-clean:
	docker-compose down -v
	docker system prune -f

# Database commands
db-seed:
	npm run db:seed

db-migrate:
	npm run db:migrate

db-reset:
	npm run db:reset

# Utility commands
logs:
	tail -f logs/app.log

logs-error:
	tail -f logs/error.log

health:
	curl -f http://localhost:3000/health || echo "Service is not healthy"

# Production deployment
deploy-prod:
	@echo "Deploying to production..."
	docker-compose -f docker-compose.yml up -d --build

# Development setup
setup-dev:
	@echo "Setting up development environment..."
	cp .env.development .env
	npm install
	docker-compose -f docker-compose.dev.yml up -d mongo redis
	@echo "Development environment ready!"

# CI/CD helpers
ci-test:
	npm ci
	npm run test
	npm run test:e2e

ci-build:
	npm run build
	docker build -t cashback-comparison-platform .

# Monitoring
monitor:
	@echo "Application Status:"
	@curl -s http://localhost:3000/health | jq . || echo "Health check failed"
	@echo "\nDocker Services:"
	@docker-compose ps
