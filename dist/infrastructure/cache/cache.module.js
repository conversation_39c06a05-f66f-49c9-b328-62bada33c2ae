"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheModule = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
const config_1 = require("@nestjs/config");
const cache_service_1 = require("./services/cache.service");
const comparison_cache_service_1 = require("./services/comparison-cache.service");
const offer_cache_service_1 = require("./services/offer-cache.service");
const platform_cache_service_1 = require("./services/platform-cache.service");
let CacheModule = class CacheModule {
};
exports.CacheModule = CacheModule;
exports.CacheModule = CacheModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            cache_manager_1.CacheModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const nodeEnv = configService.get('app.nodeEnv', 'development');
                    if (nodeEnv === 'development') {
                        return {
                            ttl: configService.get('cache.ttl.comparison', 900),
                            max: configService.get('cache.maxItems.comparison', 1000),
                        };
                    }
                    const redisConfig = configService.get('cache.redis');
                    return {
                        ttl: configService.get('cache.ttl.comparison', 900),
                        max: configService.get('cache.maxItems.comparison', 1000),
                    };
                },
                inject: [config_1.ConfigService],
            }),
        ],
        providers: [
            cache_service_1.CacheService,
            comparison_cache_service_1.ComparisonCacheService,
            offer_cache_service_1.OfferCacheService,
            platform_cache_service_1.PlatformCacheService,
        ],
        exports: [
            cache_manager_1.CacheModule,
            cache_service_1.CacheService,
            comparison_cache_service_1.ComparisonCacheService,
            offer_cache_service_1.OfferCacheService,
            platform_cache_service_1.PlatformCacheService,
        ],
    })
], CacheModule);
//# sourceMappingURL=cache.module.js.map