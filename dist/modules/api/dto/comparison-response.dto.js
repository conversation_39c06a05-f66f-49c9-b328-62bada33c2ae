"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonResultDto = exports.ComparisonCriteriaDto = exports.ComparisonMetricsDto = exports.OfferComparisonDto = exports.CashbackOfferDto = exports.OfferMetadataDto = exports.OfferTermsDto = exports.CashbackRateDto = exports.MoneyDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class MoneyDto {
    amount;
    currency;
}
exports.MoneyDto = MoneyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Amount',
        example: 5.50,
    }),
    __metadata("design:type", Number)
], MoneyDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        example: 'USD',
    }),
    __metadata("design:type", String)
], MoneyDto.prototype, "currency", void 0);
class CashbackRateDto {
    percentage;
    maxAmount;
    minSpend;
}
exports.CashbackRateDto = CashbackRateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cashback percentage',
        example: 5.5,
    }),
    __metadata("design:type", Number)
], CashbackRateDto.prototype, "percentage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum cashback amount',
        example: 25.00,
    }),
    __metadata("design:type", Number)
], CashbackRateDto.prototype, "maxAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum spend required',
        example: 50.00,
    }),
    __metadata("design:type", Number)
], CashbackRateDto.prototype, "minSpend", void 0);
class OfferTermsDto {
    description;
    exclusions;
    validFrom;
    validUntil;
    maxRedemptions;
    currentRedemptions;
}
exports.OfferTermsDto = OfferTermsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer description',
        example: '5% cashback on all purchases',
    }),
    __metadata("design:type", String)
], OfferTermsDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Offer exclusions',
        example: ['Gift cards', 'Shipping fees'],
    }),
    __metadata("design:type", Array)
], OfferTermsDto.prototype, "exclusions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Valid from date',
        example: '2024-01-01T00:00:00Z',
    }),
    __metadata("design:type", String)
], OfferTermsDto.prototype, "validFrom", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Valid until date',
        example: '2024-12-31T23:59:59Z',
    }),
    __metadata("design:type", String)
], OfferTermsDto.prototype, "validUntil", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum number of redemptions',
        example: 1000,
    }),
    __metadata("design:type", Number)
], OfferTermsDto.prototype, "maxRedemptions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Current number of redemptions',
        example: 250,
    }),
    __metadata("design:type", Number)
], OfferTermsDto.prototype, "currentRedemptions", void 0);
class OfferMetadataDto {
    category;
    subcategory;
    merchant;
    tags;
    featured;
    priority;
}
exports.OfferMetadataDto = OfferMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer category',
        example: 'shopping',
    }),
    __metadata("design:type", String)
], OfferMetadataDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Offer subcategory',
        example: 'electronics',
    }),
    __metadata("design:type", String)
], OfferMetadataDto.prototype, "subcategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Merchant name',
        example: 'Amazon',
    }),
    __metadata("design:type", String)
], OfferMetadataDto.prototype, "merchant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer tags',
        example: ['popular', 'limited-time'],
    }),
    __metadata("design:type", Array)
], OfferMetadataDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the offer is featured',
        example: true,
    }),
    __metadata("design:type", Boolean)
], OfferMetadataDto.prototype, "featured", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer priority (1-10)',
        example: 8,
    }),
    __metadata("design:type", Number)
], OfferMetadataDto.prototype, "priority", void 0);
class CashbackOfferDto {
    id;
    platformId;
    title;
    cashbackRate;
    terms;
    metadata;
    status;
    createdAt;
    updatedAt;
}
exports.CashbackOfferDto = CashbackOfferDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer ID',
        example: 'platform1_offer123',
    }),
    __metadata("design:type", String)
], CashbackOfferDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Platform ID',
        example: 'platform1',
    }),
    __metadata("design:type", String)
], CashbackOfferDto.prototype, "platformId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer title',
        example: '5% Cashback at Amazon',
    }),
    __metadata("design:type", String)
], CashbackOfferDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cashback rate details',
        type: CashbackRateDto,
    }),
    __metadata("design:type", CashbackRateDto)
], CashbackOfferDto.prototype, "cashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer terms and conditions',
        type: OfferTermsDto,
    }),
    __metadata("design:type", OfferTermsDto)
], CashbackOfferDto.prototype, "terms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer metadata',
        type: OfferMetadataDto,
    }),
    __metadata("design:type", OfferMetadataDto)
], CashbackOfferDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer status',
        example: 'active',
    }),
    __metadata("design:type", String)
], CashbackOfferDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation date',
        example: '2024-01-01T00:00:00Z',
    }),
    __metadata("design:type", String)
], CashbackOfferDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update date',
        example: '2024-01-01T00:00:00Z',
    }),
    __metadata("design:type", String)
], CashbackOfferDto.prototype, "updatedAt", void 0);
class OfferComparisonDto {
    offer;
    platformName;
    estimatedCashback;
    rank;
    score;
}
exports.OfferComparisonDto = OfferComparisonDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cashback offer details',
        type: CashbackOfferDto,
    }),
    __metadata("design:type", CashbackOfferDto)
], OfferComparisonDto.prototype, "offer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Platform name',
        example: 'Sample Cashback Platform',
    }),
    __metadata("design:type", String)
], OfferComparisonDto.prototype, "platformName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Estimated cashback amount',
        type: MoneyDto,
    }),
    __metadata("design:type", MoneyDto)
], OfferComparisonDto.prototype, "estimatedCashback", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Rank in comparison (1 = best)',
        example: 1,
    }),
    __metadata("design:type", Number)
], OfferComparisonDto.prototype, "rank", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comparison score (0-1)',
        example: 0.95,
    }),
    __metadata("design:type", Number)
], OfferComparisonDto.prototype, "score", void 0);
class ComparisonMetricsDto {
    totalOffersEvaluated;
    validOffersFound;
    averageCashbackRate;
    bestCashbackRate;
    processingTimeMs;
}
exports.ComparisonMetricsDto = ComparisonMetricsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total offers evaluated',
        example: 150,
    }),
    __metadata("design:type", Number)
], ComparisonMetricsDto.prototype, "totalOffersEvaluated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Valid offers found',
        example: 25,
    }),
    __metadata("design:type", Number)
], ComparisonMetricsDto.prototype, "validOffersFound", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average cashback rate',
        example: 3.2,
    }),
    __metadata("design:type", Number)
], ComparisonMetricsDto.prototype, "averageCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Best cashback rate found',
        example: 8.5,
    }),
    __metadata("design:type", Number)
], ComparisonMetricsDto.prototype, "bestCashbackRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Processing time in milliseconds',
        example: 245,
    }),
    __metadata("design:type", Number)
], ComparisonMetricsDto.prototype, "processingTimeMs", void 0);
class ComparisonCriteriaDto {
    spendAmount;
    category;
    merchant;
    country;
    currency;
}
exports.ComparisonCriteriaDto = ComparisonCriteriaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Spend amount',
        type: MoneyDto,
    }),
    __metadata("design:type", MoneyDto)
], ComparisonCriteriaDto.prototype, "spendAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category filter',
        example: 'shopping',
    }),
    __metadata("design:type", String)
], ComparisonCriteriaDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Merchant filter',
        example: 'Amazon',
    }),
    __metadata("design:type", String)
], ComparisonCriteriaDto.prototype, "merchant", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Country filter',
        example: 'US',
    }),
    __metadata("design:type", String)
], ComparisonCriteriaDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Currency filter',
        example: 'USD',
    }),
    __metadata("design:type", String)
], ComparisonCriteriaDto.prototype, "currency", void 0);
class ComparisonResultDto {
    id;
    criteria;
    comparisons;
    metrics;
    status;
    createdAt;
    expiresAt;
}
exports.ComparisonResultDto = ComparisonResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comparison result ID',
        example: 'comp_1640995200000_abc123',
    }),
    __metadata("design:type", String)
], ComparisonResultDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comparison criteria',
        type: ComparisonCriteriaDto,
    }),
    __metadata("design:type", ComparisonCriteriaDto)
], ComparisonResultDto.prototype, "criteria", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Offer comparisons',
        type: [OfferComparisonDto],
    }),
    __metadata("design:type", Array)
], ComparisonResultDto.prototype, "comparisons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comparison metrics',
        type: ComparisonMetricsDto,
    }),
    __metadata("design:type", ComparisonMetricsDto)
], ComparisonResultDto.prototype, "metrics", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comparison status',
        example: 'completed',
    }),
    __metadata("design:type", String)
], ComparisonResultDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation date',
        example: '2024-01-01T00:00:00Z',
    }),
    __metadata("design:type", String)
], ComparisonResultDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Expiration date',
        example: '2024-01-01T00:15:00Z',
    }),
    __metadata("design:type", String)
], ComparisonResultDto.prototype, "expiresAt", void 0);
//# sourceMappingURL=comparison-response.dto.js.map