"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var MockCashbackOfferRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockCashbackOfferRepository = void 0;
const common_1 = require("@nestjs/common");
let MockCashbackOfferRepository = MockCashbackOfferRepository_1 = class MockCashbackOfferRepository {
    logger = new common_1.Logger(MockCashbackOfferRepository_1.name);
    offers = [];
    async findById(id) {
        return this.offers.find(offer => offer.id === id) || null;
    }
    async findByPlatform(platformId, options) {
        let filtered = this.offers.filter(offer => offer.platformId === platformId);
        if (options?.status) {
            filtered = filtered.filter(offer => offer.status === options.status);
        }
        if (options?.category) {
            filtered = filtered.filter(offer => offer.belongsToCategory(options.category));
        }
        if (options?.merchant) {
            filtered = filtered.filter(offer => offer.belongsToMerchant(options.merchant));
        }
        if (options?.limit) {
            filtered = filtered.slice(0, options.limit);
        }
        return filtered;
    }
    async findAll(options) {
        let filtered = [...this.offers];
        if (options?.platformId) {
            filtered = filtered.filter(offer => offer.platformId === options.platformId);
        }
        if (options?.status) {
            filtered = filtered.filter(offer => offer.status === options.status);
        }
        if (options?.category) {
            filtered = filtered.filter(offer => offer.belongsToCategory(options.category));
        }
        if (options?.merchant) {
            filtered = filtered.filter(offer => offer.belongsToMerchant(options.merchant));
        }
        if (options?.featured !== undefined) {
            filtered = filtered.filter(offer => offer.metadata.featured === options.featured);
        }
        if (options?.validAt) {
            filtered = filtered.filter(offer => offer.isValid(options.validAt));
        }
        if (options?.limit) {
            filtered = filtered.slice(0, options.limit);
        }
        return filtered;
    }
    async findActive(options) {
        return this.findAll({ ...options, status: 'active' });
    }
    async findByCategory(category, options) {
        return this.findAll({ ...options, category });
    }
    async findByMerchant(merchant, options) {
        return this.findAll({ ...options, merchant });
    }
    async findFeatured(options) {
        return this.findAll({ ...options, featured: true });
    }
    async save(offer) {
        const existingIndex = this.offers.findIndex(o => o.id === offer.id);
        if (existingIndex >= 0) {
            this.offers[existingIndex] = offer;
        }
        else {
            this.offers.push(offer);
        }
        this.logger.debug(`Saved offer: ${offer.id}`);
        return offer;
    }
    async saveMany(offers) {
        for (const offer of offers) {
            await this.save(offer);
        }
        return offers;
    }
    async update(id, offer) {
        const existing = await this.findById(id);
        if (!existing) {
            throw new Error(`Offer not found: ${id}`);
        }
        const updated = { ...existing, ...offer };
        return this.save(updated);
    }
    async delete(id) {
        const index = this.offers.findIndex(offer => offer.id === id);
        if (index >= 0) {
            this.offers.splice(index, 1);
            this.logger.debug(`Deleted offer: ${id}`);
        }
    }
    async deleteByPlatform(platformId) {
        this.offers = this.offers.filter(offer => offer.platformId !== platformId);
        this.logger.debug(`Deleted offers for platform: ${platformId}`);
    }
    async count(options) {
        const filtered = await this.findAll(options);
        return filtered.length;
    }
    async exists(id) {
        return this.offers.some(offer => offer.id === id);
    }
};
exports.MockCashbackOfferRepository = MockCashbackOfferRepository;
exports.MockCashbackOfferRepository = MockCashbackOfferRepository = MockCashbackOfferRepository_1 = __decorate([
    (0, common_1.Injectable)()
], MockCashbackOfferRepository);
//# sourceMappingURL=mock-cashback-offer.repository.js.map