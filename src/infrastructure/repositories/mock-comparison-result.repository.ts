import { Injectable, Logger } from '@nestjs/common';
import { ComparisonResult, ComparisonCriteria } from '../../domain/entities/comparison-result.entity';
import { ComparisonResultRepository, FindComparisonOptions } from '../../domain/repositories/comparison-result.repository';

@Injectable()
export class MockComparisonResultRepository implements ComparisonResultRepository {
  private readonly logger = new Logger(MockComparisonResultRepository.name);
  private results: ComparisonResult[] = [];

  async findById(id: string): Promise<ComparisonResult | null> {
    return this.results.find(result => result.id === id) || null;
  }

  async findByCriteria(criteria: ComparisonCriteria): Promise<ComparisonResult | null> {
    // Simple criteria matching - in a real implementation this would be more sophisticated
    return this.results.find(result => 
      result.criteria.spendAmount.amount === criteria.spendAmount.amount &&
      result.criteria.spendAmount.currency === criteria.spendAmount.currency &&
      result.criteria.category === criteria.category &&
      result.criteria.merchant === criteria.merchant &&
      result.criteria.country === criteria.country &&
      result.isValid()
    ) || null;
  }

  async findAll(options?: FindComparisonOptions): Promise<ComparisonResult[]> {
    let filtered = [...this.results];
    
    if (options?.status) {
      filtered = filtered.filter(result => result.status === options.status);
    }
    
    if (options?.createdAfter) {
      filtered = filtered.filter(result => result.createdAt >= options.createdAfter!);
    }
    
    if (options?.createdBefore) {
      filtered = filtered.filter(result => result.createdAt <= options.createdBefore!);
    }
    
    if (options?.notExpired) {
      filtered = filtered.filter(result => !result.isExpired());
    }
    
    // Sort by creation date (newest first)
    filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    if (options?.limit) {
      filtered = filtered.slice(options.offset || 0, (options.offset || 0) + options.limit);
    }
    
    return filtered;
  }

  async findValid(): Promise<ComparisonResult[]> {
    return this.results.filter(result => result.isValid());
  }

  async save(result: ComparisonResult): Promise<ComparisonResult> {
    const existingIndex = this.results.findIndex(r => r.id === result.id);
    
    if (existingIndex >= 0) {
      this.results[existingIndex] = result;
    } else {
      this.results.push(result);
    }
    
    this.logger.debug(`Saved comparison result: ${result.id}`);
    return result;
  }

  async update(id: string, result: Partial<ComparisonResult>): Promise<ComparisonResult> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error(`Comparison result not found: ${id}`);
    }
    
    // In a real implementation, this would properly update the entity
    const updated = { ...existing, ...result } as ComparisonResult;
    return this.save(updated);
  }

  async delete(id: string): Promise<void> {
    const index = this.results.findIndex(result => result.id === id);
    if (index >= 0) {
      this.results.splice(index, 1);
      this.logger.debug(`Deleted comparison result: ${id}`);
    }
  }

  async deleteExpired(): Promise<number> {
    const initialCount = this.results.length;
    this.results = this.results.filter(result => !result.isExpired());
    const deletedCount = initialCount - this.results.length;
    
    if (deletedCount > 0) {
      this.logger.debug(`Deleted ${deletedCount} expired comparison results`);
    }
    
    return deletedCount;
  }

  async count(options?: FindComparisonOptions): Promise<number> {
    const filtered = await this.findAll(options);
    return filtered.length;
  }

  async exists(id: string): Promise<boolean> {
    return this.results.some(result => result.id === id);
  }
}
