import { Injectable, Logger, Inject } from '@nestjs/common';
import { ComparisonResult } from '../../../domain/entities/comparison-result.entity';
import { ComparisonResultRepository } from '../../../domain/repositories/comparison-result.repository';
import { CashbackOfferRepository } from '../../../domain/repositories/cashback-offer.repository';
import {
  CASHBACK_OFFER_REPOSITORY,
  COMPARISON_RESULT_REPOSITORY
} from '../../../infrastructure/repositories/repository.module';

export interface ComparisonAnalytics {
  totalComparisons: number;
  averageOffersPerComparison: number;
  topCategories: CategoryStats[];
  topMerchants: MerchantStats[];
  platformPerformance: PlatformStats[];
  averageProcessingTime: number;
  cacheHitRate: number;
}

export interface CategoryStats {
  category: string;
  comparisonCount: number;
  averageCashbackRate: number;
  totalOffers: number;
}

export interface MerchantStats {
  merchant: string;
  comparisonCount: number;
  averageCashbackRate: number;
  totalOffers: number;
}

export interface PlatformStats {
  platformId: string;
  platformName: string;
  totalOffers: number;
  averageCashbackRate: number;
  winRate: number; // Percentage of times this platform had the best offer
  averageRank: number;
}

export interface TrendData {
  date: string;
  comparisons: number;
  averageCashbackRate: number;
  topCategory: string;
}

@Injectable()
export class ComparisonAnalyticsService {
  private readonly logger = new Logger(ComparisonAnalyticsService.name);

  constructor(
    @Inject(COMPARISON_RESULT_REPOSITORY)
    private readonly comparisonResultRepository: ComparisonResultRepository,
    @Inject(CASHBACK_OFFER_REPOSITORY)
    private readonly offerRepository: CashbackOfferRepository,
  ) {}

  async getComparisonAnalytics(
    startDate?: Date,
    endDate?: Date,
  ): Promise<ComparisonAnalytics> {
    this.logger.log('Generating comparison analytics');

    const comparisons = await this.getComparisonsInRange(startDate, endDate);
    
    if (comparisons.length === 0) {
      return this.getEmptyAnalytics();
    }

    const [
      categoryStats,
      merchantStats,
      platformStats,
      processingStats,
    ] = await Promise.all([
      this.calculateCategoryStats(comparisons),
      this.calculateMerchantStats(comparisons),
      this.calculatePlatformStats(comparisons),
      this.calculateProcessingStats(comparisons),
    ]);

    return {
      totalComparisons: comparisons.length,
      averageOffersPerComparison: this.calculateAverageOffersPerComparison(comparisons),
      topCategories: categoryStats,
      topMerchants: merchantStats,
      platformPerformance: platformStats,
      averageProcessingTime: processingStats.averageProcessingTime,
      cacheHitRate: processingStats.cacheHitRate,
    };
  }

  async getTrendData(
    days: number = 30,
    granularity: 'day' | 'week' | 'month' = 'day',
  ): Promise<TrendData[]> {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
    
    const comparisons = await this.getComparisonsInRange(startDate, endDate);
    
    return this.groupComparisonsByPeriod(comparisons, granularity);
  }

  async getPlatformComparisonReport(platformId: string): Promise<{
    totalComparisons: number;
    winRate: number;
    averageRank: number;
    averageCashbackRate: number;
    topCategories: string[];
    recentPerformance: TrendData[];
  }> {
    const comparisons = await this.comparisonResultRepository.findAll({
      limit: 1000,
      notExpired: false,
    });

    const platformComparisons = comparisons.filter(comparison =>
      comparison.comparisons.some(comp => comp.offer.platformId === platformId)
    );

    if (platformComparisons.length === 0) {
      return {
        totalComparisons: 0,
        winRate: 0,
        averageRank: 0,
        averageCashbackRate: 0,
        topCategories: [],
        recentPerformance: [],
      };
    }

    const wins = platformComparisons.filter(comparison => {
      const bestOffer = comparison.getBestOffer();
      return bestOffer?.offer.platformId === platformId;
    }).length;

    const ranks = platformComparisons.map(comparison => {
      const platformComparison = comparison.comparisons.find(
        comp => comp.offer.platformId === platformId
      );
      return platformComparison?.rank || 999;
    });

    const cashbackRates = platformComparisons.flatMap(comparison =>
      comparison.comparisons
        .filter(comp => comp.offer.platformId === platformId)
        .map(comp => comp.offer.cashbackRate.percentage)
    );

    const categories = platformComparisons.flatMap(comparison =>
      comparison.comparisons
        .filter(comp => comp.offer.platformId === platformId)
        .map(comp => comp.offer.metadata.category)
    );

    const topCategories = this.getTopItems(categories, 5);

    return {
      totalComparisons: platformComparisons.length,
      winRate: (wins / platformComparisons.length) * 100,
      averageRank: ranks.reduce((sum, rank) => sum + rank, 0) / ranks.length,
      averageCashbackRate: cashbackRates.reduce((sum, rate) => sum + rate, 0) / cashbackRates.length,
      topCategories,
      recentPerformance: this.groupComparisonsByPeriod(platformComparisons.slice(-30), 'day'),
    };
  }

  private async getComparisonsInRange(
    startDate?: Date,
    endDate?: Date,
  ): Promise<ComparisonResult[]> {
    return this.comparisonResultRepository.findAll({
      createdAfter: startDate,
      createdBefore: endDate,
      limit: 10000, // Reasonable limit for analytics
    });
  }

  private getEmptyAnalytics(): ComparisonAnalytics {
    return {
      totalComparisons: 0,
      averageOffersPerComparison: 0,
      topCategories: [],
      topMerchants: [],
      platformPerformance: [],
      averageProcessingTime: 0,
      cacheHitRate: 0,
    };
  }

  private calculateAverageOffersPerComparison(comparisons: ComparisonResult[]): number {
    const totalOffers = comparisons.reduce(
      (sum, comparison) => sum + comparison.comparisons.length,
      0
    );
    return totalOffers / comparisons.length;
  }

  private async calculateCategoryStats(comparisons: ComparisonResult[]): Promise<CategoryStats[]> {
    const categoryMap = new Map<string, {
      count: number;
      totalCashbackRate: number;
      offerCount: number;
    }>();

    comparisons.forEach(comparison => {
      if (comparison.criteria.category) {
        const category = comparison.criteria.category;
        const existing = categoryMap.get(category) || { count: 0, totalCashbackRate: 0, offerCount: 0 };
        
        const categoryOffers = comparison.comparisons.filter(
          comp => comp.offer.metadata.category === category
        );
        
        const avgCashbackRate = categoryOffers.reduce(
          (sum, comp) => sum + comp.offer.cashbackRate.percentage, 0
        ) / categoryOffers.length;

        categoryMap.set(category, {
          count: existing.count + 1,
          totalCashbackRate: existing.totalCashbackRate + (avgCashbackRate || 0),
          offerCount: existing.offerCount + categoryOffers.length,
        });
      }
    });

    return Array.from(categoryMap.entries())
      .map(([category, stats]) => ({
        category,
        comparisonCount: stats.count,
        averageCashbackRate: stats.totalCashbackRate / stats.count,
        totalOffers: stats.offerCount,
      }))
      .sort((a, b) => b.comparisonCount - a.comparisonCount)
      .slice(0, 10);
  }

  private async calculateMerchantStats(comparisons: ComparisonResult[]): Promise<MerchantStats[]> {
    const merchantMap = new Map<string, {
      count: number;
      totalCashbackRate: number;
      offerCount: number;
    }>();

    comparisons.forEach(comparison => {
      if (comparison.criteria.merchant) {
        const merchant = comparison.criteria.merchant;
        const existing = merchantMap.get(merchant) || { count: 0, totalCashbackRate: 0, offerCount: 0 };
        
        const merchantOffers = comparison.comparisons.filter(
          comp => comp.offer.metadata.merchant === merchant
        );
        
        const avgCashbackRate = merchantOffers.reduce(
          (sum, comp) => sum + comp.offer.cashbackRate.percentage, 0
        ) / merchantOffers.length;

        merchantMap.set(merchant, {
          count: existing.count + 1,
          totalCashbackRate: existing.totalCashbackRate + (avgCashbackRate || 0),
          offerCount: existing.offerCount + merchantOffers.length,
        });
      }
    });

    return Array.from(merchantMap.entries())
      .map(([merchant, stats]) => ({
        merchant,
        comparisonCount: stats.count,
        averageCashbackRate: stats.totalCashbackRate / stats.count,
        totalOffers: stats.offerCount,
      }))
      .sort((a, b) => b.comparisonCount - a.comparisonCount)
      .slice(0, 10);
  }

  private async calculatePlatformStats(comparisons: ComparisonResult[]): Promise<PlatformStats[]> {
    const platformMap = new Map<string, {
      totalOffers: number;
      totalCashbackRate: number;
      wins: number;
      totalRank: number;
      appearances: number;
      name: string;
    }>();

    comparisons.forEach(comparison => {
      const bestOffer = comparison.getBestOffer();
      
      comparison.comparisons.forEach(comp => {
        const platformId = comp.offer.platformId;
        const existing = platformMap.get(platformId) || {
          totalOffers: 0,
          totalCashbackRate: 0,
          wins: 0,
          totalRank: 0,
          appearances: 0,
          name: comp.platformName,
        };

        platformMap.set(platformId, {
          totalOffers: existing.totalOffers + 1,
          totalCashbackRate: existing.totalCashbackRate + comp.offer.cashbackRate.percentage,
          wins: existing.wins + (bestOffer?.offer.platformId === platformId ? 1 : 0),
          totalRank: existing.totalRank + comp.rank,
          appearances: existing.appearances + 1,
          name: comp.platformName,
        });
      });
    });

    return Array.from(platformMap.entries())
      .map(([platformId, stats]) => ({
        platformId,
        platformName: stats.name,
        totalOffers: stats.totalOffers,
        averageCashbackRate: stats.totalCashbackRate / stats.totalOffers,
        winRate: (stats.wins / stats.appearances) * 100,
        averageRank: stats.totalRank / stats.appearances,
      }))
      .sort((a, b) => b.winRate - a.winRate);
  }

  private calculateProcessingStats(comparisons: ComparisonResult[]): {
    averageProcessingTime: number;
    cacheHitRate: number;
  } {
    const processingTimes = comparisons.map(comp => comp.metrics.processingTimeMs);
    const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;

    // Cache hit rate calculation would require additional tracking
    // For now, return a placeholder
    const cacheHitRate = 0; // TODO: Implement cache hit tracking

    return {
      averageProcessingTime,
      cacheHitRate,
    };
  }

  private groupComparisonsByPeriod(
    comparisons: ComparisonResult[],
    granularity: 'day' | 'week' | 'month',
  ): TrendData[] {
    const groups = new Map<string, {
      comparisons: number;
      totalCashbackRate: number;
      categories: string[];
    }>();

    comparisons.forEach(comparison => {
      const date = this.formatDateByGranularity(comparison.createdAt, granularity);
      const existing = groups.get(date) || { comparisons: 0, totalCashbackRate: 0, categories: [] };
      
      const avgCashbackRate = comparison.metrics.averageCashbackRate;
      const category = comparison.criteria.category;

      groups.set(date, {
        comparisons: existing.comparisons + 1,
        totalCashbackRate: existing.totalCashbackRate + avgCashbackRate,
        categories: category ? [...existing.categories, category] : existing.categories,
      });
    });

    return Array.from(groups.entries())
      .map(([date, stats]) => ({
        date,
        comparisons: stats.comparisons,
        averageCashbackRate: stats.totalCashbackRate / stats.comparisons,
        topCategory: this.getTopItems(stats.categories, 1)[0] || 'N/A',
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private formatDateByGranularity(date: Date, granularity: 'day' | 'week' | 'month'): string {
    switch (granularity) {
      case 'day':
        return date.toISOString().split('T')[0];
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        return weekStart.toISOString().split('T')[0];
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      default:
        return date.toISOString().split('T')[0];
    }
  }

  private getTopItems(items: string[], count: number): string[] {
    const frequency = new Map<string, number>();
    items.forEach(item => {
      frequency.set(item, (frequency.get(item) || 0) + 1);
    });

    return Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([item]) => item);
  }
}
