import { Injectable, Logger } from '@nestjs/common';
import { CashbackPlatform } from '../../domain/entities/cashback-platform.entity';
import { CashbackPlatformRepository, FindPlatformsOptions } from '../../domain/repositories/cashback-platform.repository';

@Injectable()
export class MockCashbackPlatformRepository implements CashbackPlatformRepository {
  private readonly logger = new Logger(MockCashbackPlatformRepository.name);
  private platforms: CashbackPlatform[] = [];

  async findById(id: string): Promise<CashbackPlatform | null> {
    return this.platforms.find(platform => platform.id === id) || null;
  }

  async findByName(name: string): Promise<CashbackPlatform | null> {
    return this.platforms.find(platform => platform.name === name) || null;
  }

  async findAll(options?: FindPlatformsOptions): Promise<CashbackPlatform[]> {
    let filtered = [...this.platforms];
    
    if (options?.status) {
      filtered = filtered.filter(platform => platform.status === options.status);
    }
    
    if (options?.country) {
      filtered = filtered.filter(platform => platform.supportsCountry(options.country!));
    }
    
    if (options?.currency) {
      filtered = filtered.filter(platform => platform.supportsCurrency(options.currency!));
    }
    
    if (options?.category) {
      filtered = filtered.filter(platform => platform.hasCategory(options.category!));
    }
    
    if (options?.limit) {
      filtered = filtered.slice(options.offset || 0, (options.offset || 0) + options.limit);
    }
    
    return filtered;
  }

  async findActive(): Promise<CashbackPlatform[]> {
    return this.platforms.filter(platform => platform.isActive());
  }

  async save(platform: CashbackPlatform): Promise<CashbackPlatform> {
    const existingIndex = this.platforms.findIndex(p => p.id === platform.id);
    
    if (existingIndex >= 0) {
      this.platforms[existingIndex] = platform;
    } else {
      this.platforms.push(platform);
    }
    
    this.logger.debug(`Saved platform: ${platform.id}`);
    return platform;
  }

  async update(id: string, platform: Partial<CashbackPlatform>): Promise<CashbackPlatform> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error(`Platform not found: ${id}`);
    }
    
    // In a real implementation, this would properly update the entity
    const updated = { ...existing, ...platform } as CashbackPlatform;
    return this.save(updated);
  }

  async delete(id: string): Promise<void> {
    const index = this.platforms.findIndex(platform => platform.id === id);
    if (index >= 0) {
      this.platforms.splice(index, 1);
      this.logger.debug(`Deleted platform: ${id}`);
    }
  }

  async count(options?: FindPlatformsOptions): Promise<number> {
    const filtered = await this.findAll(options);
    return filtered.length;
  }

  async exists(id: string): Promise<boolean> {
    return this.platforms.some(platform => platform.id === id);
  }
}
