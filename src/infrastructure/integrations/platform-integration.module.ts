import { Modu<PERSON>, OnModuleInit } from '@nestjs/common';
import { PlatformIntegrationStrategy } from './strategy/platform-integration.strategy';
import { SamplePlatformIntegration } from './implementations/sample-platform.integration';

@Module({
  providers: [
    PlatformIntegrationStrategy,
    SamplePlatformIntegration,
  ],
  exports: [PlatformIntegrationStrategy],
})
export class PlatformIntegrationModule implements OnModuleInit {
  constructor(
    private readonly integrationStrategy: PlatformIntegrationStrategy,
    private readonly samplePlatformIntegration: SamplePlatformIntegration,
  ) {}

  async onModuleInit() {
    // Register all platform integrations
    this.integrationStrategy.registerIntegration(this.samplePlatformIntegration);
    
    // Add more platform integrations here as they are implemented
    // this.integrationStrategy.registerIntegration(this.anotherPlatformIntegration);
  }
}
