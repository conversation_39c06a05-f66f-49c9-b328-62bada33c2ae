import { CashbackOffer } from '../../domain/entities/cashback-offer.entity';
import { CashbackOfferRepository, FindOffersOptions } from '../../domain/repositories/cashback-offer.repository';
export declare class MockCashbackOfferRepository implements CashbackOfferRepository {
    private readonly logger;
    private offers;
    findById(id: string): Promise<CashbackOffer | null>;
    findByPlatform(platformId: string, options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findAll(options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findActive(options?: Omit<FindOffersOptions, 'status'>): Promise<CashbackOffer[]>;
    findByCategory(category: string, options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findByMerchant(merchant: string, options?: FindOffersOptions): Promise<CashbackOffer[]>;
    findFeatured(options?: FindOffersOptions): Promise<CashbackOffer[]>;
    save(offer: CashbackOffer): Promise<CashbackOffer>;
    saveMany(offers: CashbackOffer[]): Promise<CashbackOffer[]>;
    update(id: string, offer: Partial<CashbackOffer>): Promise<CashbackOffer>;
    delete(id: string): Promise<void>;
    deleteByPlatform(platformId: string): Promise<void>;
    count(options?: FindOffersOptions): Promise<number>;
    exists(id: string): Promise<boolean>;
}
