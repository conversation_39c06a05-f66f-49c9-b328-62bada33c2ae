"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@nestjs/config");
exports.default = (0, config_1.registerAs)('database', () => ({
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/cashback-comparison',
    testUri: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/cashback-comparison-test',
    options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
    },
}));
//# sourceMappingURL=database.config.js.map