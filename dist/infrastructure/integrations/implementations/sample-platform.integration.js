"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SamplePlatformIntegration = void 0;
const common_1 = require("@nestjs/common");
const base_platform_integration_1 = require("../base/base-platform-integration");
const cashback_offer_entity_1 = require("../../../domain/entities/cashback-offer.entity");
const cashback_platform_entity_1 = require("../../../domain/entities/cashback-platform.entity");
const cashback_rate_vo_1 = require("../../../domain/value-objects/cashback-rate.vo");
const platform_status_enum_1 = require("../../../domain/enums/platform-status.enum");
let SamplePlatformIntegration = class SamplePlatformIntegration extends base_platform_integration_1.BasePlatformIntegration {
    constructor() {
        const configuration = {
            apiEndpoint: 'https://api.sample-cashback-platform.com/v1',
            apiKey: process.env.SAMPLE_PLATFORM_API_KEY,
            rateLimit: 1000,
            timeout: 30000,
            retryAttempts: 3,
        };
        super('sample-platform', configuration);
    }
    async getPlatformInfo() {
        const metadata = {
            website: 'https://sample-cashback-platform.com',
            description: 'Sample cashback platform for demonstration purposes',
            supportedCountries: ['US', 'CA', 'UK'],
            supportedCurrencies: ['USD', 'CAD', 'GBP'],
            categories: ['shopping', 'travel', 'dining', 'entertainment', 'groceries'],
        };
        return new cashback_platform_entity_1.CashbackPlatform(this.platformId, 'Sample Cashback Platform', platform_status_enum_1.PlatformStatus.ACTIVE, metadata, this.configuration);
    }
    async fetchOffers() {
        try {
            const response = await this.retryWithBackoff(async () => {
                return this.makeApiRequest('/offers');
            });
            return response.offers.map(apiOffer => this.mapApiOfferToEntity(apiOffer));
        }
        catch (error) {
            this.logger.error('Failed to fetch offers from Sample Platform', error);
            throw new Error(`Failed to fetch offers: ${error.message}`);
        }
    }
    async fetchOffersByCategory(category) {
        try {
            const response = await this.retryWithBackoff(async () => {
                return this.makeApiRequest(`/offers?category=${encodeURIComponent(category)}`);
            });
            return response.offers.map(apiOffer => this.mapApiOfferToEntity(apiOffer));
        }
        catch (error) {
            this.logger.error(`Failed to fetch offers by category ${category} from Sample Platform`, error);
            throw new Error(`Failed to fetch offers by category: ${error.message}`);
        }
    }
    async fetchOffersByMerchant(merchant) {
        try {
            const response = await this.retryWithBackoff(async () => {
                return this.makeApiRequest(`/offers?merchant=${encodeURIComponent(merchant)}`);
            });
            return response.offers.map(apiOffer => this.mapApiOfferToEntity(apiOffer));
        }
        catch (error) {
            this.logger.error(`Failed to fetch offers by merchant ${merchant} from Sample Platform`, error);
            throw new Error(`Failed to fetch offers by merchant: ${error.message}`);
        }
    }
    async validateConfiguration() {
        if (!this.configuration.apiKey) {
            throw new Error('API key is required for Sample Platform integration');
        }
        if (!this.configuration.apiEndpoint) {
            throw new Error('API endpoint is required for Sample Platform integration');
        }
        try {
            await this.makeApiRequest('/health');
            return true;
        }
        catch (error) {
            throw new Error(`Configuration validation failed: ${error.message}`);
        }
    }
    async getSupportedCategories() {
        try {
            const response = await this.makeApiRequest('/categories');
            return response.categories;
        }
        catch (error) {
            this.logger.error('Failed to fetch supported categories from Sample Platform', error);
            return ['shopping', 'travel', 'dining', 'entertainment', 'groceries'];
        }
    }
    async getSupportedMerchants() {
        try {
            const response = await this.makeApiRequest('/merchants');
            return response.merchants;
        }
        catch (error) {
            this.logger.error('Failed to fetch supported merchants from Sample Platform', error);
            return [];
        }
    }
    async performHealthCheck() {
        try {
            const response = await this.makeApiRequest('/health');
            if (response.status !== 'ok') {
                throw new Error(`Platform health check failed: ${response.status}`);
            }
        }
        catch (error) {
            throw new Error(`Health check failed: ${error.message}`);
        }
    }
    mapApiOfferToEntity(apiOffer) {
        const cashbackRate = new cashback_rate_vo_1.CashbackRate(apiOffer.cashback_percentage, apiOffer.max_cashback, apiOffer.min_spend);
        const terms = {
            description: apiOffer.description,
            validFrom: new Date(apiOffer.valid_from),
            validUntil: apiOffer.valid_until ? new Date(apiOffer.valid_until) : undefined,
        };
        const metadata = {
            category: apiOffer.category,
            merchant: apiOffer.merchant,
            tags: apiOffer.tags,
            featured: apiOffer.featured,
            priority: apiOffer.priority,
        };
        const status = this.mapApiStatusToOfferStatus(apiOffer.status);
        return new cashback_offer_entity_1.CashbackOffer(`${this.platformId}_${apiOffer.id}`, this.platformId, apiOffer.title, cashbackRate, terms, metadata, status);
    }
    mapApiStatusToOfferStatus(apiStatus) {
        switch (apiStatus.toLowerCase()) {
            case 'active':
                return platform_status_enum_1.OfferStatus.ACTIVE;
            case 'expired':
                return platform_status_enum_1.OfferStatus.EXPIRED;
            case 'coming_soon':
                return platform_status_enum_1.OfferStatus.COMING_SOON;
            case 'paused':
                return platform_status_enum_1.OfferStatus.PAUSED;
            default:
                return platform_status_enum_1.OfferStatus.ACTIVE;
        }
    }
};
exports.SamplePlatformIntegration = SamplePlatformIntegration;
exports.SamplePlatformIntegration = SamplePlatformIntegration = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], SamplePlatformIntegration);
//# sourceMappingURL=sample-platform.integration.js.map