import { PlatformStatus } from '../enums/platform-status.enum';
export interface PlatformConfiguration {
    apiEndpoint?: string;
    apiKey?: string;
    rateLimit?: number;
    timeout?: number;
    retryAttempts?: number;
}
export interface PlatformMetadata {
    website: string;
    description: string;
    supportedCountries: string[];
    supportedCurrencies: string[];
    categories: string[];
}
export declare class CashbackPlatform {
    private readonly _id;
    private readonly _name;
    private readonly _status;
    private readonly _metadata;
    private readonly _configuration;
    private readonly _createdAt;
    private _updatedAt;
    constructor(_id: string, _name: string, _status: PlatformStatus, _metadata: PlatformMetadata, _configuration: PlatformConfiguration, _createdAt?: Date, _updatedAt?: Date);
    get id(): string;
    get name(): string;
    get status(): PlatformStatus;
    get metadata(): PlatformMetadata;
    get configuration(): PlatformConfiguration;
    get createdAt(): Date;
    get updatedAt(): Date;
    private validateId;
    private validateName;
    private validateMetadata;
    isActive(): boolean;
    supportsCountry(countryCode: string): boolean;
    supportsCurrency(currencyCode: string): boolean;
    hasCategory(category: string): boolean;
    updateStatus(newStatus: PlatformStatus): CashbackPlatform;
    touch(): void;
    equals(other: CashbackPlatform): boolean;
    toString(): string;
}
