"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingModule = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const config_1 = require("@nestjs/config");
const winston = require("winston");
const path = require("path");
let LoggingModule = class LoggingModule {
};
exports.LoggingModule = LoggingModule;
exports.LoggingModule = LoggingModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            nest_winston_1.WinstonModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    const logLevel = configService.get('app.logging.level', 'info');
                    const logFilePath = configService.get('app.logging.filePath', 'logs/app.log');
                    const nodeEnv = configService.get('app.nodeEnv', 'development');
                    const logDir = path.dirname(logFilePath);
                    const customFormat = winston.format.combine(winston.format.timestamp({
                        format: 'YYYY-MM-DD HH:mm:ss.SSS',
                    }), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
                        const logEntry = {
                            timestamp,
                            level,
                            message,
                            ...meta,
                        };
                        if (context)
                            logEntry.context = context;
                        if (trace)
                            logEntry.trace = trace;
                        return JSON.stringify(logEntry);
                    }));
                    const transports = [];
                    if (nodeEnv === 'development') {
                        transports.push(new winston.transports.Console({
                            level: logLevel,
                            format: winston.format.combine(winston.format.colorize(), winston.format.timestamp({
                                format: 'HH:mm:ss.SSS',
                            }), winston.format.printf(({ timestamp, level, message, context }) => {
                                const contextStr = context ? ` [${context}]` : '';
                                return `${timestamp} ${level}${contextStr}: ${message}`;
                            })),
                        }));
                    }
                    transports.push(new winston.transports.File({
                        filename: logFilePath,
                        level: logLevel,
                        format: customFormat,
                        maxsize: 10 * 1024 * 1024,
                        maxFiles: 5,
                        tailable: true,
                    }), new winston.transports.File({
                        filename: path.join(logDir, 'error.log'),
                        level: 'error',
                        format: customFormat,
                        maxsize: 10 * 1024 * 1024,
                        maxFiles: 5,
                        tailable: true,
                    }));
                    if (nodeEnv === 'production') {
                        const httpEndpoint = configService.get('logging.httpEndpoint');
                        if (httpEndpoint) {
                            transports.push(new winston.transports.Http({
                                host: httpEndpoint,
                                port: 443,
                                path: '/logs',
                                ssl: true,
                                level: 'error',
                                format: customFormat,
                            }));
                        }
                    }
                    return {
                        level: logLevel,
                        format: customFormat,
                        transports,
                        exceptionHandlers: [
                            new winston.transports.File({
                                filename: path.join(logDir, 'exceptions.log'),
                                format: customFormat,
                            }),
                        ],
                        rejectionHandlers: [
                            new winston.transports.File({
                                filename: path.join(logDir, 'rejections.log'),
                                format: customFormat,
                            }),
                        ],
                        exitOnError: false,
                    };
                },
                inject: [config_1.ConfigService],
            }),
        ],
        exports: [nest_winston_1.WinstonModule],
    })
], LoggingModule);
//# sourceMappingURL=logging.module.js.map