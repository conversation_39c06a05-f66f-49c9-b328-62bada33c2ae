import { HealthCheckService, HealthCheckResult } from '@nestjs/terminus';
import { DatabaseHealthIndicator } from './indicators/database-health.indicator';
import { CacheHealthIndicator } from './indicators/cache-health.indicator';
import { PlatformHealthIndicator } from './indicators/platform-health.indicator';
export declare class HealthController {
    private health;
    private databaseHealthIndicator;
    private cacheHealthIndicator;
    private platformHealthIndicator;
    constructor(health: HealthCheckService, databaseHealthIndicator: DatabaseHealthIndicator, cacheHealthIndicator: CacheHealthIndicator, platformHealthIndicator: PlatformHealthIndicator);
    check(): Promise<HealthCheckResult>;
    checkDatabase(): Promise<HealthCheckResult>;
    checkCache(): Promise<HealthCheckResult>;
    checkPlatforms(): Promise<HealthCheckResult>;
}
