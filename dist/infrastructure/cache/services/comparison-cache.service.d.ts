import { CacheService } from './cache.service';
import { ComparisonResult, ComparisonCriteria } from '../../../domain/entities/comparison-result.entity';
export declare class ComparisonCacheService {
    private readonly cacheService;
    private readonly logger;
    private readonly namespace;
    constructor(cacheService: CacheService);
    getComparison(criteria: ComparisonCriteria): Promise<ComparisonResult | null>;
    setComparison(result: ComparisonResult): Promise<void>;
    getComparisonById(id: string): Promise<ComparisonResult | null>;
    setComparisonById(result: ComparisonResult): Promise<void>;
    invalidateComparison(criteria: ComparisonCriteria): Promise<void>;
    invalidateComparisonById(id: string): Promise<void>;
    getRecentComparisons(limit?: number): Promise<ComparisonResult[]>;
    cacheComparisonHistory(results: ComparisonResult[], key?: string): Promise<void>;
    getComparisonHistory(key?: string): Promise<ComparisonResult[]>;
    invalidateExpiredComparisons(): Promise<void>;
    getComparisonStats(): Promise<{
        totalCached: number;
        hitRate: number;
        averageAge: number;
    }>;
    private buildCriteriaKey;
    private calculateTtl;
    warmupCache(commonCriteria: ComparisonCriteria[], resultFactory: (criteria: ComparisonCriteria) => Promise<ComparisonResult>): Promise<void>;
    preloadPopularComparisons(): Promise<void>;
}
