import { registerAs } from '@nestjs/config';

export default registerAs('cache', () => ({
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'cashback:',
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
  },
  ttl: {
    comparison: parseInt(process.env.CACHE_COMPARISON_TTL || '900', 10), // 15 minutes
    offers: parseInt(process.env.CACHE_OFFERS_TTL || '3600', 10), // 1 hour
    platforms: parseInt(process.env.CACHE_PLATFORMS_TTL || '7200', 10), // 2 hours
    analytics: parseInt(process.env.CACHE_ANALYTICS_TTL || '1800', 10), // 30 minutes
    health: parseInt(process.env.CACHE_HEALTH_TTL || '300', 10), // 5 minutes
  },
  maxItems: {
    comparison: parseInt(process.env.CACHE_MAX_COMPARISONS || '10000', 10),
    offers: parseInt(process.env.CACHE_MAX_OFFERS || '50000', 10),
    platforms: parseInt(process.env.CACHE_MAX_PLATFORMS || '100', 10),
    analytics: parseInt(process.env.CACHE_MAX_ANALYTICS || '1000', 10),
  },
}));
