"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
const config_1 = require("@nestjs/config");
let CacheService = CacheService_1 = class CacheService {
    cacheManager;
    configService;
    logger = new common_1.Logger(CacheService_1.name);
    constructor(cacheManager, configService) {
        this.cacheManager = cacheManager;
        this.configService = configService;
    }
    async get(key, namespace) {
        try {
            const fullKey = this.buildKey(key, namespace);
            const value = await this.cacheManager.get(fullKey);
            if (value) {
                this.logger.debug(`Cache hit for key: ${fullKey}`);
            }
            else {
                this.logger.debug(`Cache miss for key: ${fullKey}`);
            }
            return value || null;
        }
        catch (error) {
            this.logger.error(`Failed to get cache value for key: ${key}`, error);
            return null;
        }
    }
    async set(key, value, options = {}) {
        try {
            const fullKey = this.buildKey(key, options.namespace);
            const ttl = options.ttl || this.getDefaultTtl(options.namespace);
            await this.cacheManager.set(fullKey, value, ttl);
            this.logger.debug(`Cache set for key: ${fullKey}, TTL: ${ttl}s`);
        }
        catch (error) {
            this.logger.error(`Failed to set cache value for key: ${key}`, error);
        }
    }
    async del(key, namespace) {
        try {
            const fullKey = this.buildKey(key, namespace);
            await this.cacheManager.del(fullKey);
            this.logger.debug(`Cache deleted for key: ${fullKey}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete cache value for key: ${key}`, error);
        }
    }
    async mget(keys, namespace) {
        try {
            const fullKeys = keys.map(key => this.buildKey(key, namespace));
            const values = await Promise.all(fullKeys.map(key => this.cacheManager.get(key)));
            return values.map(value => value || null);
        }
        catch (error) {
            this.logger.error(`Failed to get multiple cache values`, error);
            return keys.map(() => null);
        }
    }
    async mset(keyValuePairs, options = {}) {
        try {
            const ttl = options.ttl || this.getDefaultTtl(options.namespace);
            await Promise.all(keyValuePairs.map(({ key, value }) => {
                const fullKey = this.buildKey(key, options.namespace);
                return this.cacheManager.set(fullKey, value, ttl);
            }));
            this.logger.debug(`Cache set for ${keyValuePairs.length} keys, TTL: ${ttl}s`);
        }
        catch (error) {
            this.logger.error(`Failed to set multiple cache values`, error);
        }
    }
    async mdel(keys, namespace) {
        try {
            const fullKeys = keys.map(key => this.buildKey(key, namespace));
            await Promise.all(fullKeys.map(key => this.cacheManager.del(key)));
            this.logger.debug(`Cache deleted for ${keys.length} keys`);
        }
        catch (error) {
            this.logger.error(`Failed to delete multiple cache values`, error);
        }
    }
    async clear(namespace) {
        try {
            if (namespace) {
                this.logger.warn(`Namespace-specific cache clearing not fully implemented for: ${namespace}`);
            }
            else {
                const store = this.cacheManager.store;
                if (store && typeof store.reset === 'function') {
                    await store.reset();
                    this.logger.log('Cache cleared completely');
                }
                else {
                    this.logger.warn('Cache reset not available in current store implementation');
                }
            }
        }
        catch (error) {
            this.logger.error('Failed to clear cache', error);
        }
    }
    async exists(key, namespace) {
        try {
            const fullKey = this.buildKey(key, namespace);
            const value = await this.cacheManager.get(fullKey);
            return value !== undefined && value !== null;
        }
        catch (error) {
            this.logger.error(`Failed to check cache existence for key: ${key}`, error);
            return false;
        }
    }
    async getOrSet(key, factory, options = {}) {
        const cached = await this.get(key, options.namespace);
        if (cached !== null) {
            return cached;
        }
        const value = await factory();
        await this.set(key, value, options);
        return value;
    }
    async wrap(key, factory, options = {}) {
        return this.getOrSet(key, factory, options);
    }
    buildKey(key, namespace) {
        if (namespace) {
            return `${namespace}:${key}`;
        }
        return key;
    }
    getDefaultTtl(namespace) {
        const ttlConfig = this.configService.get('cache.ttl');
        switch (namespace) {
            case 'comparison':
                return ttlConfig.comparison;
            case 'offers':
                return ttlConfig.offers;
            case 'platforms':
                return ttlConfig.platforms;
            case 'analytics':
                return ttlConfig.analytics;
            case 'health':
                return ttlConfig.health;
            default:
                return ttlConfig.comparison;
        }
    }
    async getStats() {
        return {
            hits: 0,
            misses: 0,
            keys: 0,
            hitRate: 0,
        };
    }
    async healthCheck() {
        try {
            const testKey = 'health_check';
            const testValue = Date.now().toString();
            await this.set(testKey, testValue, { ttl: 10 });
            const retrieved = await this.get(testKey);
            await this.del(testKey);
            return retrieved === testValue;
        }
        catch (error) {
            this.logger.error('Cache health check failed', error);
            return false;
        }
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = CacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object, config_1.ConfigService])
], CacheService);
//# sourceMappingURL=cache.service.js.map