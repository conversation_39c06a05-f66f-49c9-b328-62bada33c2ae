export declare class CashbackRate {
    private readonly _percentage;
    private readonly _maxAmount?;
    private readonly _minSpend?;
    constructor(_percentage: number, _maxAmount?: number | undefined, _minSpend?: number | undefined);
    get percentage(): number;
    get maxAmount(): number | undefined;
    get minSpend(): number | undefined;
    private validatePercentage;
    private validateMaxAmount;
    private validateMinSpend;
    calculateCashback(spendAmount: number): number;
    equals(other: CashbackRate): boolean;
    toString(): string;
}
