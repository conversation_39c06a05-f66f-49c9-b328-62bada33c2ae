import { ComparisonService } from '../../../domain/services/comparison.service';
import { ComparisonResult, ComparisonCriteria } from '../../../domain/entities/comparison-result.entity';
import { CashbackOfferRepository } from '../../../domain/repositories/cashback-offer.repository';
import { CashbackPlatformRepository } from '../../../domain/repositories/cashback-platform.repository';
import { ComparisonResultRepository } from '../../../domain/repositories/comparison-result.repository';
import { PlatformIntegrationStrategy } from '../../../infrastructure/integrations/strategy/platform-integration.strategy';
export interface ComparisonRequest {
    spendAmount: number;
    currency?: string;
    category?: string;
    merchant?: string;
    country?: string;
    useCache?: boolean;
    maxCacheAge?: number;
}
export interface ComparisonOptions {
    includeInactive?: boolean;
    maxResults?: number;
    minCashbackRate?: number;
    platformIds?: string[];
}
export declare class ComparisonEngineService {
    private readonly comparisonService;
    private readonly offerRepository;
    private readonly platformRepository;
    private readonly comparisonResultRepository;
    private readonly platformIntegrationStrategy;
    private readonly logger;
    constructor(comparisonService: ComparisonService, offerRepository: CashbackOfferRepository, platformRepository: CashbackPlatformRepository, comparisonResultRepository: ComparisonResultRepository, platformIntegrationStrategy: PlatformIntegrationStrategy);
    compareOffers(request: ComparisonRequest, options?: ComparisonOptions): Promise<ComparisonResult>;
    refreshComparison(comparisonId: string): Promise<ComparisonResult>;
    syncAndCompare(request: ComparisonRequest, options?: ComparisonOptions): Promise<ComparisonResult>;
    getComparisonHistory(criteria: Partial<ComparisonCriteria>, limit?: number): Promise<ComparisonResult[]>;
    cleanupExpiredComparisons(): Promise<number>;
    private getCachedComparison;
    private fetchRelevantOffers;
    private getPlatformNames;
    private applyResultFiltering;
    private cacheComparisonResult;
}
