import { ComparisonResult, ComparisonCriteria } from '../entities/comparison-result.entity';
import { ComparisonStatus } from '../enums/platform-status.enum';
export interface FindComparisonOptions {
    status?: ComparisonStatus;
    createdAfter?: Date;
    createdBefore?: Date;
    notExpired?: boolean;
    limit?: number;
    offset?: number;
}
export interface ComparisonResultRepository {
    findById(id: string): Promise<ComparisonResult | null>;
    findByCriteria(criteria: ComparisonCriteria): Promise<ComparisonResult | null>;
    findAll(options?: FindComparisonOptions): Promise<ComparisonResult[]>;
    findValid(): Promise<ComparisonResult[]>;
    save(result: ComparisonResult): Promise<ComparisonResult>;
    update(id: string, result: Partial<ComparisonResult>): Promise<ComparisonResult>;
    delete(id: string): Promise<void>;
    deleteExpired(): Promise<number>;
    count(options?: FindComparisonOptions): Promise<number>;
    exists(id: string): Promise<boolean>;
}
