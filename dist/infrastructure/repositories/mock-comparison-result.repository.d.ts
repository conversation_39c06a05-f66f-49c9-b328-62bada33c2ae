import { ComparisonResult, ComparisonCriteria } from '../../domain/entities/comparison-result.entity';
import { ComparisonResultRepository, FindComparisonOptions } from '../../domain/repositories/comparison-result.repository';
export declare class MockComparisonResultRepository implements ComparisonResultRepository {
    private readonly logger;
    private results;
    findById(id: string): Promise<ComparisonResult | null>;
    findByCriteria(criteria: ComparisonCriteria): Promise<ComparisonResult | null>;
    findAll(options?: FindComparisonOptions): Promise<ComparisonResult[]>;
    findValid(): Promise<ComparisonResult[]>;
    save(result: ComparisonResult): Promise<ComparisonResult>;
    update(id: string, result: Partial<ComparisonResult>): Promise<ComparisonResult>;
    delete(id: string): Promise<void>;
    deleteExpired(): Promise<number>;
    count(options?: FindComparisonOptions): Promise<number>;
    exists(id: string): Promise<boolean>;
}
