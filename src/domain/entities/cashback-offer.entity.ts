import { CashbackRate } from '../value-objects/cashback-rate.vo';
import { Money } from '../value-objects/money.vo';
import { OfferStatus } from '../enums/platform-status.enum';

export interface OfferTerms {
  description: string;
  exclusions?: string[];
  validFrom: Date;
  validUntil?: Date;
  maxRedemptions?: number;
  currentRedemptions?: number;
}

export interface OfferMetadata {
  category: string;
  subcategory?: string;
  merchant: string;
  tags: string[];
  featured: boolean;
  priority: number;
}

export class CashbackOffer {
  constructor(
    private readonly _id: string,
    private readonly _platformId: string,
    private readonly _title: string,
    private readonly _cashbackRate: CashbackRate,
    private readonly _terms: OfferTerms,
    private readonly _metadata: OfferMetadata,
    private readonly _status: OfferStatus = OfferStatus.ACTIVE,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date(),
  ) {
    this.validateId();
    this.validatePlatformId();
    this.validateTitle();
    this.validateTerms();
    this.validateMetadata();
  }

  get id(): string {
    return this._id;
  }

  get platformId(): string {
    return this._platformId;
  }

  get title(): string {
    return this._title;
  }

  get cashbackRate(): CashbackRate {
    return this._cashbackRate;
  }

  get terms(): OfferTerms {
    return this._terms;
  }

  get metadata(): OfferMetadata {
    return this._metadata;
  }

  get status(): OfferStatus {
    return this._status;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  private validateId(): void {
    if (!this._id || this._id.trim().length === 0) {
      throw new Error('Offer ID cannot be empty');
    }
  }

  private validatePlatformId(): void {
    if (!this._platformId || this._platformId.trim().length === 0) {
      throw new Error('Platform ID cannot be empty');
    }
  }

  private validateTitle(): void {
    if (!this._title || this._title.trim().length === 0) {
      throw new Error('Offer title cannot be empty');
    }
  }

  private validateTerms(): void {
    if (!this._terms.description) {
      throw new Error('Offer description is required');
    }
    if (this._terms.validUntil && this._terms.validUntil <= this._terms.validFrom) {
      throw new Error('Valid until date must be after valid from date');
    }
  }

  private validateMetadata(): void {
    if (!this._metadata.category) {
      throw new Error('Offer category is required');
    }
    if (!this._metadata.merchant) {
      throw new Error('Offer merchant is required');
    }
  }

  isActive(): boolean {
    return this._status === OfferStatus.ACTIVE;
  }

  isValid(date: Date = new Date()): boolean {
    if (!this.isActive()) {
      return false;
    }

    if (date < this._terms.validFrom) {
      return false;
    }

    if (this._terms.validUntil && date > this._terms.validUntil) {
      return false;
    }

    if (this._terms.maxRedemptions && 
        this._terms.currentRedemptions && 
        this._terms.currentRedemptions >= this._terms.maxRedemptions) {
      return false;
    }

    return true;
  }

  calculateCashback(spendAmount: Money): Money {
    if (!this.isValid()) {
      return new Money(0, spendAmount.currency);
    }

    const cashbackAmount = this._cashbackRate.calculateCashback(spendAmount.amount);
    return new Money(cashbackAmount, spendAmount.currency);
  }

  belongsToCategory(category: string): boolean {
    return this._metadata.category.toLowerCase() === category.toLowerCase();
  }

  belongsToMerchant(merchant: string): boolean {
    return this._metadata.merchant.toLowerCase() === merchant.toLowerCase();
  }

  hasTag(tag: string): boolean {
    return this._metadata.tags.some(t => t.toLowerCase() === tag.toLowerCase());
  }

  touch(): void {
    this._updatedAt = new Date();
  }

  equals(other: CashbackOffer): boolean {
    return this._id === other._id && this._platformId === other._platformId;
  }

  toString(): string {
    return `${this._title} - ${this._cashbackRate.toString()} (${this._metadata.merchant})`;
  }
}
